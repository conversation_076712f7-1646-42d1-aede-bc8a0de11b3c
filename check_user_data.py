#!/usr/bin/env python3
"""
Check user and role data
"""
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.session import AsyncSessionLocal
from app.models.user import User
from app.models.role import Role
from sqlalchemy import select

async def check_data():
    async with AsyncSessionLocal() as db:
        # Get admin user
        result = await db.execute(
            select(User, Role)
            .join(Role, User.role_id == Role.id)
            .where(User.email == '<EMAIL>')
        )
        
        user_role = result.first()
        if user_role:
            user, role = user_role
            print(f"User: {user.email}")
            print(f"  organization_id: {user.organization_id}")
            print(f"  role_id: {user.role_id}")
            print(f"Role: {role.name}")
            print(f"  company_id: {role.company_id}")
        else:
            print("No admin user found")

if __name__ == "__main__":
    asyncio.run(check_data())
