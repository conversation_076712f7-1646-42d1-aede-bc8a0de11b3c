import asyncio
import random
from datetime import datetime, timedelta
from faker import Faker
from sqlalchemy import delete, insert, select, text
from sqlalchemy.ext.asyncio import AsyncSession
from uuid_extensions import uuid7

from app.core.security import get_password_hash
from app.db.session import get_async_db
from app.models.activity_log import ActivityLog
from app.models.asset import Asset, AssetType
from app.models.associations import conversation_label_association, user_role_association
from app.models.canned_response import CannedResponse
from app.models.chat import Conversation, ConversationStatus, Message
from app.models.conversation_note import ConversationNote
from app.models.customer import Customer
from app.models.label import Label
from app.models.note import Note
from app.models.organization import Organization
from app.models.role import Role
from app.models.team import Team
from app.models.user import User

fake = Faker()

async def clear_data(db: AsyncSession):
    """Deletes all data from the tables in the correct order."""
    print("🗑️ Deleting existing data...")
    # Set default_team_id to NULL to break the circular dependency
    await db.execute(text("UPDATE organizations SET default_team_id = NULL"))
    await db.commit()

    # Delete in reverse order of creation to respect foreign keys
    await db.execute(delete(conversation_label_association))
    await db.execute(delete(user_role_association))
    await db.execute(delete(ActivityLog))
    await db.execute(delete(ConversationNote))
    await db.execute(delete(Note))
    await db.execute(delete(Message))
    await db.execute(delete(CannedResponse))
    await db.execute(delete(Conversation))
    await db.execute(delete(Label))
    await db.execute(delete(Customer))
    await db.execute(delete(User))
    await db.execute(delete(Asset))
    await db.execute(delete(Role))
    await db.execute(delete(Team))
    await db.execute(delete(Organization))
    await db.commit()
    print("✅ Existing data deleted.")

async def create_test_data():
    """Creates a comprehensive set of interconnected test data."""
    async for db in get_async_db():
        await clear_data(db)

        print("🏢 Creating organizations...")
        orgs = [Organization(
            name=fake.company(),
            description=fake.catch_phrase(),
            website=fake.url(),
            email=fake.company_email(),
            phone=fake.phone_number(),
            address=fake.address(),
        ) for _ in range(5)]
        db.add_all(orgs)
        await db.flush()

        print("👥 Creating teams...")
        teams = []
        for org in orgs:
            for i in range(random.randint(1, 3)):
                teams.append(Team(
                    name=f"{fake.job().title()} Team",
                    description=fake.sentence(),
                    organization_id=org.id,
                ))
        db.add_all(teams)
        await db.flush()

        print("🔐 Creating roles...")
        role_names = ["Support Tier 1", "Support Tier 2", "Sales", "Manager"]
        roles = []
        for org in orgs:
            for role_name in role_names:
                roles.append(Role(
                    name=role_name,
                    description=f"{role_name} role for {org.name}",
                    company_id=org.id,
                ))
        db.add_all(roles)
        await db.flush()

        print("👤 Creating users and assets...")
        users = []
        assets = []
        for _ in range(30):
            org = random.choice(orgs)
            org_teams = [t for t in teams if t.organization_id == org.id]
            team = random.choice(org_teams) if org_teams else None
            org_roles = [r for r in roles if r.company_id == org.id]
            role = random.choice(org_roles) if org_roles else None

            asset = Asset(
                filename=f"{uuid7()}.webp",
                original_filename=f"{fake.word()}.jpg",
                file_type=AssetType.image,
                mime_type="image/webp",
                file_size=random.randint(10000, 50000),
                s3_bucket="yupcha-test-bucket",
                s3_key=f"profiles/{uuid7()}.webp",
                s3_url=fake.image_url(),
                is_public=True,
            )
            assets.append(asset)

            user = User(
                full_name=fake.name(),
                email=fake.unique.email(),
                hashed_password=get_password_hash("password123"),
                organization_id=org.id,
                team_id=team.id if team else None,
                role_id=role.id if role else None,
                profile_image=asset,
            )
            users.append(user)
        db.add_all(assets)
        db.add_all(users)
        await db.flush()

        print("🛒 Creating customers...")
        customers = [Customer(
            customer_id=str(uuid7()),
            name=fake.name(),
            email=fake.email(),
            organization_id=random.choice(orgs).id,
        ) for _ in range(50)]
        db.add_all(customers)
        await db.flush()

        print("💬 Creating conversations...")
        conversations = []
        for _ in range(40):
            customer = random.choice(customers)
            org_teams = [t for t in teams if t.organization_id == customer.organization_id]
            assigned_team = random.choice(org_teams) if org_teams else None
            conversation = Conversation(
                customer_id=customer.id,
                organization_id=customer.organization_id,
                status=random.choice(list(ConversationStatus)),
                assigned_team_id=assigned_team.id if assigned_team else None,
            )
            conversations.append(conversation)
        db.add_all(conversations)
        await db.flush()

        print("🏷️ Creating labels...")
        label_defs = {"Urgent": "#D32F2F", "Question": "#1976D2", "Bug": "#C2185B"}
        labels = []
        for org in orgs:
            for name, color in label_defs.items():
                labels.append(Label(name=name, color=color, organization_id=org.id))
        db.add_all(labels)
        await db.flush()

        print("🔗 Linking labels to conversations...")
        label_associations = []
        for conv in conversations:
            org_labels = [l for l in labels if l.organization_id == conv.organization_id]
            if org_labels:
                for label in random.sample(org_labels, random.randint(1, len(org_labels))):
                    label_associations.append({"conversation_id": conv.id, "label_id": label.id})
        if label_associations:
            await db.execute(insert(conversation_label_association).values(label_associations))
            await db.flush()

        print("✍️ Creating messages...")
        messages = []
        for conv in conversations:
            org_users = [u for u in users if u.organization_id == conv.organization_id]
            if not org_users: continue
            for i in range(random.randint(2, 12)):
                sender_type = "customer" if i % 2 == 0 else "agent"
                messages.append(Message(
                    conversation_id=conv.id,
                    customer_id=conv.customer_id,
                    user_id=random.choice(org_users).id if sender_type == "agent" else None,
                    content=fake.sentence(),
                    sender=sender_type,
                ))
        db.add_all(messages)
        await db.flush()

        print("🗒️ Creating conversation notes...")
        conv_notes = []
        for conv in conversations:
            org_users = [u for u in users if u.organization_id == conv.organization_id]
            if not org_users: continue
            for _ in range(random.randint(0, 2)):
                conv_notes.append(ConversationNote(
                    content=fake.paragraph(),
                    conversation_id=conv.id,
                    user_id=random.choice(org_users).id,
                ))
        db.add_all(conv_notes)
        await db.flush()

        print("📝 Creating canned responses...")
        canned_responses = []
        for org in orgs:
            org_users = [u for u in users if u.organization_id == org.id]
            if org_users:
                for _ in range(3):
                    canned_responses.append(CannedResponse(
                        title=fake.sentence(nb_words=3),
                        content=fake.text(),
                        organization_id=org.id,
                        created_by=random.choice(org_users).id,
                    ))
        db.add_all(canned_responses)
        await db.flush()

        print("📜 Creating activity logs...")
        activity_logs = []
        for user in users:
            for _ in range(random.randint(1, 5)):
                activity_logs.append(ActivityLog(
                    user_id=user.id,
                    action=random.choice(["user.login", "conversation.view", "note.create"]),
                    ip_address=fake.ipv4(),
                ))
        db.add_all(activity_logs)
        await db.flush()

        print("📝 Creating general customer notes...")
        # General Customer Notes
        notes = []
        for customer in customers:
            org_users = [u for u in users if u.organization_id == customer.organization_id]
            if not org_users: continue
            if random.random() > 0.5: # 50% chance to have a note
                for _ in range(random.randint(1, 2)):
                    notes.append(Note(
                        content=fake.paragraph(nb_sentences=3),
                        customer_id=customer.id,
                        user_id=random.choice(org_users).id,
                    ))
        db.add_all(notes)
        await db.flush()

        await db.commit()
        print("✅ Test data created successfully!")

if __name__ == "__main__":
    asyncio.run(create_test_data())