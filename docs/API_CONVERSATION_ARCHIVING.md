# Conversation Archiving API Documentation

## Overview

The Conversation Archiving feature allows agents and administrators to archive conversations, effectively hiding them from default views while preserving them for future reference. This feature is essential for maintaining clean conversation lists while ensuring no data is lost.

## Features

- **Archive conversations**: Move conversations to archived status
- **Unarchive conversations**: Restore archived conversations to active status
- **Filtered listing**: Control visibility of archived conversations
- **Audit logging**: Track all archive/unarchive actions
- **Status management**: Proper state transitions

## API Endpoints

### 1. List Conversations (Enhanced)

**Endpoint:** `GET /api/v1/conversations/`

**Description:** List conversations with optional inclusion of archived conversations.

**Query Parameters:**
- `status` (optional): Filter by conversation status (`new`, `open`, `closed`, `archived`)
- `team_id` (optional): Filter by assigned team ID
- `customer_id` (optional): Filter by customer ID
- `unassigned` (optional): Filter for unassigned conversations (boolean)
- `include_archived` (optional): Include archived conversations in results (boolean, default: false)
- `page` (optional): Page number for pagination (default: 1)
- `size` (optional): Number of items per page (default: 50)

**Response:**
```json
{
  "items": [
    {
      "id": "06874e73-0b6b-766d-8000-12dfb3637338",
      "customer_id": "06874e71-bde4-7bee-8000-beeab28b920e",
      "organization_id": "06874bc0-ac71-75ea-8000-8666ab57b647",
      "status": "open",
      "created_at": "2025-07-14T11:17:04.418439Z",
      "updated_at": "2025-07-15T09:24:13.883852Z",
      "assigned_team_id": "06874bc0-d9a8-7f5f-8000-f10493c71870",
      "labels": []
    }
  ],
  "total": 3,
  "page": 1,
  "size": 50,
  "pages": 1
}
```

**Examples:**
```bash
# Get active conversations only (default)
GET /api/v1/conversations/

# Get all conversations including archived
GET /api/v1/conversations/?include_archived=true

# Get only archived conversations
GET /api/v1/conversations/?status=archived&include_archived=true

# Get conversations for specific team
GET /api/v1/conversations/?team_id=06874bc0-d9a8-7f5f-8000-f10493c71870
```

### 2. Archive Conversation

**Endpoint:** `POST /api/v1/conversations/{conversation_id}/archive`

**Description:** Archives a conversation, changing its status to 'archived' and hiding it from default lists.

**Path Parameters:**
- `conversation_id` (required): UUID of the conversation to archive

**Authentication:** Requires agent or admin permissions

**Response:**
```json
{
  "id": "06874e73-0b6b-766d-8000-12dfb3637338",
  "customer_id": "06874e71-bde4-7bee-8000-beeab28b920e",
  "organization_id": "06874bc0-ac71-75ea-8000-8666ab57b647",
  "status": "archived",
  "created_at": "2025-07-14T11:17:04.418439Z",
  "updated_at": "2025-07-15T09:25:19.289917Z",
  "assigned_team_id": "06874bc0-d9a8-7f5f-8000-f10493c71870",
  "labels": []
}
```

**Error Responses:**
- `404 Not Found`: Conversation not found
- `403 Forbidden`: Insufficient permissions
- `500 Internal Server Error`: Server error

**Example:**
```bash
curl -X POST "http://localhost:8002/api/v1/conversations/06874e73-0b6b-766d-8000-12dfb3637338/archive" \
  -H "Cookie: session=your_session_cookie"
```

### 3. Unarchive Conversation

**Endpoint:** `POST /api/v1/conversations/{conversation_id}/unarchive`

**Description:** Unarchives a conversation, changing its status from 'archived' to 'closed' and making it visible in default lists.

**Path Parameters:**
- `conversation_id` (required): UUID of the conversation to unarchive

**Authentication:** Requires agent or admin permissions

**Response:**
```json
{
  "id": "06874e73-0b6b-766d-8000-12dfb3637338",
  "customer_id": "06874e71-bde4-7bee-8000-beeab28b920e",
  "organization_id": "06874bc0-ac71-75ea-8000-8666ab57b647",
  "status": "closed",
  "created_at": "2025-07-14T11:17:04.418439Z",
  "updated_at": "2025-07-15T09:26:45.123456Z",
  "assigned_team_id": "06874bc0-d9a8-7f5f-8000-f10493c71870",
  "labels": []
}
```

**Error Responses:**
- `404 Not Found`: Conversation not found or was not archived
- `403 Forbidden`: Insufficient permissions
- `500 Internal Server Error`: Server error

**Example:**
```bash
curl -X POST "http://localhost:8002/api/v1/conversations/06874e73-0b6b-766d-8000-12dfb3637338/unarchive" \
  -H "Cookie: session=your_session_cookie"
```

## Conversation Status Flow

```
new → open → closed → archived
 ↑      ↑      ↑        ↓
 └──────┴──────┴────────┘
                    (unarchive)
```

**Status Descriptions:**
- `new`: Newly created conversation, not yet assigned
- `open`: Active conversation being handled by an agent
- `closed`: Conversation completed by agent
- `archived`: Conversation archived for long-term storage

## Behavior Details

### Default Filtering
- **Standard lists**: Archived conversations are hidden by default
- **Explicit inclusion**: Use `include_archived=true` to see archived conversations
- **Status filtering**: Can filter specifically for archived conversations

### State Transitions
- **Archive**: Any status can be archived
- **Unarchive**: Only archived conversations can be unarchived
- **Unarchive target**: Unarchived conversations return to 'closed' status

### Audit Logging
All archive and unarchive actions are logged with:
- User who performed the action
- Timestamp of the action
- Conversation ID affected
- Action type (`conversation.archive` or `conversation.unarchive`)

## Frontend Integration

### JavaScript Examples

```javascript
// Archive a conversation
async function archiveConversation(conversationId) {
  const response = await fetch(`/api/v1/conversations/${conversationId}/archive`, {
    method: 'POST',
    credentials: 'include'
  });
  
  if (response.ok) {
    const archivedConversation = await response.json();
    console.log('Conversation archived:', archivedConversation);
    // Refresh conversation list
    loadConversations();
  }
}

// Load conversations with archive option
async function loadConversations(includeArchived = false) {
  const url = `/api/v1/conversations/?include_archived=${includeArchived}`;
  const response = await fetch(url, { credentials: 'include' });
  const data = await response.json();
  
  return data.items;
}

// Get only archived conversations
async function getArchivedConversations() {
  const url = '/api/v1/conversations/?status=archived&include_archived=true';
  const response = await fetch(url, { credentials: 'include' });
  const data = await response.json();
  
  return data.items;
}
```

### React Component Example

```jsx
function ConversationList() {
  const [conversations, setConversations] = useState([]);
  const [showArchived, setShowArchived] = useState(false);

  const loadConversations = async () => {
    const url = `/api/v1/conversations/?include_archived=${showArchived}`;
    const response = await fetch(url, { credentials: 'include' });
    const data = await response.json();
    setConversations(data.items);
  };

  const archiveConversation = async (id) => {
    await fetch(`/api/v1/conversations/${id}/archive`, {
      method: 'POST',
      credentials: 'include'
    });
    loadConversations();
  };

  return (
    <div>
      <label>
        <input 
          type="checkbox" 
          checked={showArchived}
          onChange={(e) => setShowArchived(e.target.checked)}
        />
        Show archived conversations
      </label>
      
      {conversations.map(conv => (
        <div key={conv.id}>
          <span>{conv.status}</span>
          {conv.status !== 'archived' && (
            <button onClick={() => archiveConversation(conv.id)}>
              Archive
            </button>
          )}
        </div>
      ))}
    </div>
  );
}
```

## Testing

Use the provided test files to verify the archiving functionality:

1. **Customer Side**: `CU_TEST.py` - Simulates customer interactions
2. **Agent Side**: `AG_TEST.py` - Simulates agent interactions with archive features

### Test Scenarios

1. **Basic Archive Flow**:
   - Create conversation via customer client
   - Connect as agent and respond
   - Archive the conversation
   - Verify it's hidden from default list
   - Verify it appears with `include_archived=true`

2. **Unarchive Flow**:
   - Archive a conversation
   - Unarchive it
   - Verify it appears in default list again
   - Verify status changed to 'closed'

3. **Filtering Tests**:
   - Test various filter combinations
   - Verify archived conversations are properly filtered
   - Test pagination with archived conversations
