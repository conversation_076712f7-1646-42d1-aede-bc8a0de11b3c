[project]
name = "yupcha-customerbot-ai"
version = "0.1.0"
description = "FastAPI-based customer chatbot with WebSocket support"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi[all]",
    "sqlalchemy",
    "psycopg2-binary",
    "asyncpg",
    "pydantic",
    "pydantic-settings",
    "uvicorn",
    "python-dotenv",
    "alembic",
    "scalar-fastapi",
    "itsdangerous",
    "fastapi-mail",
    "requests>=2.32.4",
    "websockets>=12.0",
    "boto3",
    "pillow",
    "python-multipart",
    "aiohttp>=3.12.12",
    "bcrypt==4.0.1",
    "passlib==1.7.4",
    "apscheduler",
    "fastapi-cache2[redis]>=0.2.2",
    "aioconsole>=0.8.1",
    "broadcaster[redis]>=0.3.1",
    "redis>=4.6.0",
    "casbin",
    "casbin-sqlalchemy-adapter>=1.4.0",
    "faker>=37.4.0",
    "typer",
    "fastapi-pagination[sqlalchemy]",
    "uuid7",
    "uuid-extensions>=0.0.1",
]
