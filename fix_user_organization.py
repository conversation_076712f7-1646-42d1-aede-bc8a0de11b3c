#!/usr/bin/env python3
"""
Quick script to fix user organization_id data
"""
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.session import AsyncSessionLocal
from app.models.user import User
from app.models.role import Role
from sqlalchemy import select, update

async def fix_user_organization():
    async with AsyncSessionLocal() as db:
        # Get all users with null organization_id but have a role with company_id
        result = await db.execute(
            select(User, Role)
            .join(Role, User.role_id == Role.id)
            .where(User.organization_id.is_(None))
            .where(Role.company_id.is_not(None))
        )
        
        users_to_fix = result.all()
        
        for user, role in users_to_fix:
            print(f"Fixing user {user.email}: setting organization_id to {role.company_id}")
            await db.execute(
                update(User)
                .where(User.id == user.id)
                .values(organization_id=role.company_id)
            )
        
        await db.commit()
        print(f"Fixed {len(users_to_fix)} users")

if __name__ == "__main__":
    asyncio.run(fix_user_organization())
