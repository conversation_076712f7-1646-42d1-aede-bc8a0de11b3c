#!/usr/bin/env python3
"""
Comprehensive API testing script for UUID-based routes
"""
import requests
import json
import sys
from typing import Dict, Any, Optional

BASE_URL = "http://localhost:8000"

class APITester:
    def __init__(self):
        self.session = requests.Session()
        self.access_token = None
        self.user_id = None
        self.org_id = None
        self.team_id = None
        self.customer_id = None
        self.conversation_id = None
        
    def test_endpoint(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                     headers: Optional[Dict] = None, expected_status: int = 200) -> Dict[str, Any]:
        """Test an API endpoint and return the response"""
        url = f"{BASE_URL}{endpoint}"
        
        if headers is None:
            headers = {}

        # Cookie-based auth is handled automatically by the session
        
        print(f"\n🔍 Testing {method.upper()} {endpoint}")
        
        try:
            if method.upper() == "GET":
                response = self.session.get(url, headers=headers)
            elif method.upper() == "POST":
                response = self.session.post(url, json=data, headers=headers)
            elif method.upper() == "PUT":
                response = self.session.put(url, json=data, headers=headers)
            elif method.upper() == "DELETE":
                response = self.session.delete(url, headers=headers)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == expected_status:
                print(f"   ✅ Success")
                try:
                    return response.json()
                except:
                    return {"status": "success", "text": response.text}
            else:
                print(f"   ❌ Expected {expected_status}, got {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                    return error_data
                except:
                    print(f"   Error: {response.text}")
                    return {"error": response.text}
                    
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return {"error": str(e)}
    
    def run_tests(self):
        """Run comprehensive API tests"""
        print("🚀 Starting comprehensive API tests for UUID-based routes")
        
        # 1. Health check
        print("\n" + "="*50)
        print("1. HEALTH CHECK")
        print("="*50)
        health = self.test_endpoint("GET", "/health")
        
        # 2. Authentication
        print("\n" + "="*50)
        print("2. AUTHENTICATION")
        print("="*50)
        
        # Login (using form data for OAuth2PasswordRequestForm)
        login_data = {
            "username": "<EMAIL>",
            "password": "adminpassword"
        }

        # Use form data instead of JSON for OAuth2PasswordRequestForm
        response = self.session.post(
            f"{BASE_URL}/api/v1/auth/login",
            data=login_data  # Use data instead of json for form submission
        )

        print(f"\n🔍 Testing POST /api/v1/auth/login")
        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            print(f"   ✅ Login successful (cookie-based auth)")
            # The session cookie is automatically stored in self.session
        else:
            print(f"   ❌ Login failed")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Error: {response.text}")
            return
        
        # Get current user
        user_response = self.test_endpoint("GET", "/api/v1/auth/me")
        if "id" in user_response:
            self.user_id = user_response["id"]
            self.org_id = user_response.get("company_id")
            self.team_id = user_response.get("team_id")
            print(f"   👤 User ID: {self.user_id}")
            print(f"   🏢 Org ID: {self.org_id}")
            print(f"   👥 Team ID: {self.team_id}")
        
        # 3. Organizations
        print("\n" + "="*50)
        print("3. ORGANIZATIONS")
        print("="*50)
        
        # List organizations
        orgs = self.test_endpoint("GET", "/api/v1/organizations")

        # Get specific organization
        if self.org_id:
            org_detail = self.test_endpoint("GET", f"/api/v1/organizations/{self.org_id}")

        # 4. Teams
        print("\n" + "="*50)
        print("4. TEAMS")
        print("="*50)

        # List teams
        teams = self.test_endpoint("GET", "/api/v1/teams")

        # Get specific team
        if self.team_id:
            team_detail = self.test_endpoint("GET", f"/api/v1/teams/{self.team_id}")

        # 5. Users
        print("\n" + "="*50)
        print("5. USERS")
        print("="*50)

        # List users
        users = self.test_endpoint("GET", "/api/v1/users")

        # Get specific user
        if self.user_id:
            user_detail = self.test_endpoint("GET", f"/api/v1/users/{self.user_id}")

        # 6. Roles
        print("\n" + "="*50)
        print("6. ROLES")
        print("="*50)

        # List roles
        roles = self.test_endpoint("GET", "/api/v1/roles")
        
        # 7. Customers
        print("\n" + "="*50)
        print("7. CUSTOMERS")
        print("="*50)
        
        # List customers
        customers_response = self.test_endpoint("GET", "/api/v1/customers")

        # Create a test customer
        customer_data = {
            "customer_id": "test-customer-001",
            "name": "Test Customer",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "organization_id": self.org_id
        }

        new_customer = self.test_endpoint("POST", "/api/v1/customers", data=customer_data, expected_status=201)
        if "id" in new_customer:
            self.customer_id = new_customer["id"]
            print(f"   🛒 Created customer ID: {self.customer_id}")

            # Get the created customer
            customer_detail = self.test_endpoint("GET", f"/api/v1/customers/{self.customer_id}")
        
        # 8. Conversations
        print("\n" + "="*50)
        print("8. CONVERSATIONS")
        print("="*50)
        
        # List conversations
        conversations = self.test_endpoint("GET", "/api/v1/conversations")

        # Create a test conversation
        if self.customer_id and self.org_id:
            conversation_data = {
                "customer_id": self.customer_id,
                "organization_id": self.org_id,
                "status": "new"
            }

            new_conversation = self.test_endpoint("POST", "/api/v1/conversations", data=conversation_data, expected_status=201)
            if "id" in new_conversation:
                self.conversation_id = new_conversation["id"]
                print(f"   💬 Created conversation ID: {self.conversation_id}")

                # Get the created conversation
                conv_detail = self.test_endpoint("GET", f"/api/v1/conversations/{self.conversation_id}")

        # 9. Messages
        print("\n" + "="*50)
        print("9. MESSAGES")
        print("="*50)

        # List messages
        messages = self.test_endpoint("GET", "/api/v1/messages")

        # Create a test message
        if self.conversation_id and self.customer_id:
            message_data = {
                "conversation_id": self.conversation_id,
                "customer_id": self.customer_id,
                "content": "Hello, this is a test message!",
                "sender": "customer",
                "message_type": "text"
            }

            new_message = self.test_endpoint("POST", "/api/v1/messages", data=message_data, expected_status=201)
            if "id" in new_message:
                message_id = new_message["id"]
                print(f"   📝 Created message ID: {message_id}")

                # Get the created message
                message_detail = self.test_endpoint("GET", f"/api/v1/messages/{message_id}")

        # 10. Canned Responses
        print("\n" + "="*50)
        print("10. CANNED RESPONSES")
        print("="*50)

        # List canned responses
        canned_responses = self.test_endpoint("GET", "/api/v1/canned-responses")

        # Create a test canned response
        if self.org_id and self.user_id:
            canned_data = {
                "title": "Test Response",
                "content": "This is a test canned response",
                "shortcut": "test",
                "category": "greeting",
                "organization_id": self.org_id,
                "created_by": self.user_id,
                "is_active": True,
                "is_public": True
            }

            new_canned = self.test_endpoint("POST", "/api/v1/canned-responses", data=canned_data, expected_status=201)
            if "id" in new_canned:
                canned_id = new_canned["id"]
                print(f"   📋 Created canned response ID: {canned_id}")

                # Get the created canned response
                canned_detail = self.test_endpoint("GET", f"/api/v1/canned-responses/{canned_id}")
        
        print("\n" + "="*50)
        print("✅ API TESTING COMPLETED")
        print("="*50)
        print("All UUID-based routes have been tested!")

if __name__ == "__main__":
    tester = APITester()
    tester.run_tests()
