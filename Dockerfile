# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Set the working directory in the container
WORKDIR /app

# Install uv
RUN pip install uv

# Copy the dependency files to the working directory
COPY pyproject.toml uv.lock ./

# Install any needed packages specified in pyproject.toml and uv.lock
RUN uv pip install --system --no-cache --extra all -r pyproject.toml

# Copy the rest of the application's code to the working directory
COPY . .

# Make the startup script executable
RUN chmod +x /app/scripts/start.sh

# Instead of CMD, use ENTRYPOINT to run the script
ENTRYPOINT ["/app/scripts/start.sh"]
