<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Chat Test - Single Role System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .chat-section {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .chat-messages {
            height: 300px;
            border: 1px solid #ddd;
            padding: 10px;
            overflow-y: auto;
            margin-bottom: 10px;
            background-color: #fafafa;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        .user-message {
            background-color: #e3f2fd;
            text-align: right;
        }
        .customer-message {
            background-color: #f3e5f5;
            text-align: left;
        }
        .system-message {
            background-color: #fff3e0;
            text-align: center;
            font-style: italic;
        }
        .input-group {
            display: flex;
            gap: 10px;
        }
        input[type="text"] {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 8px 16px;
            background-color: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #1976d2;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected {
            background-color: #c8e6c9;
            color: #2e7d32;
        }
        .disconnected {
            background-color: #ffcdd2;
            color: #c62828;
        }
        .auth-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .user-info {
            background-color: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>🚀 WebSocket Chat Test - Single Role System</h1>
    
    <div class="auth-section">
        <h3>Authentication</h3>
        <div class="input-group">
            <input type="email" id="email" placeholder="Email (<EMAIL>)" value="<EMAIL>">
            <input type="password" id="password" placeholder="Password" value="adminpassword">
            <button onclick="login()">Login</button>
            <button onclick="logout()">Logout</button>
        </div>
        <div id="userInfo" class="user-info" style="display: none;"></div>
    </div>

    <div class="container">
        <!-- User/Agent Chat -->
        <div class="chat-section">
            <h3>👨‍💼 User/Agent Chat (Authenticated)</h3>
            <div id="userStatus" class="status disconnected">Disconnected</div>
            <div id="userMessages" class="chat-messages"></div>
            <div class="input-group">
                <input type="text" id="userMessageInput" placeholder="Type your message..." disabled>
                <button onclick="sendUserMessage()" id="userSendBtn" disabled>Send</button>
                <button onclick="connectAsUser()" id="userConnectBtn">Connect</button>
                <button onclick="disconnectUser()" id="userDisconnectBtn" disabled>Disconnect</button>
            </div>
        </div>

        <!-- Customer Chat -->
        <div class="chat-section">
            <h3>👤 Customer Chat</h3>
            <div id="customerStatus" class="status disconnected">Disconnected</div>
            <div id="customerMessages" class="chat-messages"></div>
            <div class="input-group">
                <input type="text" id="customerMessageInput" placeholder="Type your message..." disabled>
                <button onclick="sendCustomerMessage()" id="customerSendBtn" disabled>Send</button>
                <button onclick="connectAsCustomer()" id="customerConnectBtn">Connect</button>
                <button onclick="disconnectCustomer()" id="customerDisconnectBtn" disabled>Disconnect</button>
            </div>
        </div>
    </div>

    <script>
        let userWs = null;
        let customerWs = null;
        let conversationId = 1; // Test conversation ID
        let isAuthenticated = false;
        let currentUser = null;

        // Authentication functions
        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`,
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('Login successful:', result);
                    await getCurrentUser();
                } else {
                    alert('Login failed');
                }
            } catch (error) {
                console.error('Login error:', error);
                alert('Login error: ' + error.message);
            }
        }

        async function getCurrentUser() {
            try {
                const response = await fetch('/api/v1/auth/me', {
                    credentials: 'include'
                });
                
                if (response.ok) {
                    currentUser = await response.json();
                    isAuthenticated = true;
                    displayUserInfo();
                } else {
                    isAuthenticated = false;
                    currentUser = null;
                    hideUserInfo();
                }
            } catch (error) {
                console.error('Get user error:', error);
                isAuthenticated = false;
                currentUser = null;
                hideUserInfo();
            }
        }

        function displayUserInfo() {
            const userInfo = document.getElementById('userInfo');
            userInfo.style.display = 'block';
            userInfo.innerHTML = `
                <strong>Logged in as:</strong> ${currentUser.full_name} (${currentUser.email})<br>
                <strong>Role:</strong> ${currentUser.role ? currentUser.role.name : 'Agent (default)'}<br>
                <strong>Admin:</strong> ${currentUser.is_admin ? 'Yes' : 'No'}
            `;
        }

        function hideUserInfo() {
            document.getElementById('userInfo').style.display = 'none';
        }

        async function logout() {
            try {
                await fetch('/api/v1/auth/logout', {
                    method: 'POST',
                    credentials: 'include'
                });
                isAuthenticated = false;
                currentUser = null;
                hideUserInfo();
                disconnectUser();
                disconnectCustomer();
            } catch (error) {
                console.error('Logout error:', error);
            }
        }

        // WebSocket functions for User/Agent
        function connectAsUser() {
            if (!isAuthenticated) {
                alert('Please login first');
                return;
            }

            const wsUrl = `ws://localhost:8000/api/v1/ws/chat/${conversationId}`;
            userWs = new WebSocket(wsUrl);

            userWs.onopen = function() {
                document.getElementById('userStatus').textContent = 'Connected as ' + (currentUser.role ? currentUser.role.name : 'Agent');
                document.getElementById('userStatus').className = 'status connected';
                document.getElementById('userMessageInput').disabled = false;
                document.getElementById('userSendBtn').disabled = false;
                document.getElementById('userConnectBtn').disabled = true;
                document.getElementById('userDisconnectBtn').disabled = false;
                addUserMessage('system', 'Connected to conversation as ' + (currentUser.role ? currentUser.role.name : 'Agent'));
            };

            userWs.onmessage = function(event) {
                const data = JSON.parse(event.data);
                console.log('User received:', data);
                
                if (data.type === 'message') {
                    addUserMessage('customer', `Customer: ${data.content}`);
                } else if (data.type === 'system') {
                    addUserMessage('system', data.detail);
                }
            };

            userWs.onclose = function() {
                document.getElementById('userStatus').textContent = 'Disconnected';
                document.getElementById('userStatus').className = 'status disconnected';
                document.getElementById('userMessageInput').disabled = true;
                document.getElementById('userSendBtn').disabled = true;
                document.getElementById('userConnectBtn').disabled = false;
                document.getElementById('userDisconnectBtn').disabled = true;
                addUserMessage('system', 'Disconnected from conversation');
            };

            userWs.onerror = function(error) {
                console.error('User WebSocket error:', error);
                addUserMessage('system', 'Connection error');
            };
        }

        function disconnectUser() {
            if (userWs) {
                userWs.close();
                userWs = null;
            }
        }

        function sendUserMessage() {
            const input = document.getElementById('userMessageInput');
            const message = input.value.trim();
            
            if (message && userWs && userWs.readyState === WebSocket.OPEN) {
                const messageData = {
                    type: 'text',
                    content: message
                };
                
                userWs.send(JSON.stringify(messageData));
                addUserMessage('user', `You: ${message}`);
                input.value = '';
            }
        }

        function addUserMessage(type, content) {
            const messages = document.getElementById('userMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.textContent = content;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // WebSocket functions for Customer
        function connectAsCustomer() {
            const customerId = 'test-customer-123';
            const wsUrl = `ws://localhost:8000/api/v1/ws/chat/${conversationId}?customer_id=${customerId}`;
            customerWs = new WebSocket(wsUrl);

            customerWs.onopen = function() {
                document.getElementById('customerStatus').textContent = 'Connected as Customer';
                document.getElementById('customerStatus').className = 'status connected';
                document.getElementById('customerMessageInput').disabled = false;
                document.getElementById('customerSendBtn').disabled = false;
                document.getElementById('customerConnectBtn').disabled = true;
                document.getElementById('customerDisconnectBtn').disabled = false;
                addCustomerMessage('system', 'Connected to conversation as Customer');
            };

            customerWs.onmessage = function(event) {
                const data = JSON.parse(event.data);
                console.log('Customer received:', data);
                
                if (data.type === 'message') {
                    addCustomerMessage('user', `Agent: ${data.content}`);
                } else if (data.type === 'system') {
                    addCustomerMessage('system', data.detail);
                }
            };

            customerWs.onclose = function() {
                document.getElementById('customerStatus').textContent = 'Disconnected';
                document.getElementById('customerStatus').className = 'status disconnected';
                document.getElementById('customerMessageInput').disabled = true;
                document.getElementById('customerSendBtn').disabled = true;
                document.getElementById('customerConnectBtn').disabled = false;
                document.getElementById('customerDisconnectBtn').disabled = true;
                addCustomerMessage('system', 'Disconnected from conversation');
            };

            customerWs.onerror = function(error) {
                console.error('Customer WebSocket error:', error);
                addCustomerMessage('system', 'Connection error');
            };
        }

        function disconnectCustomer() {
            if (customerWs) {
                customerWs.close();
                customerWs = null;
            }
        }

        function sendCustomerMessage() {
            const input = document.getElementById('customerMessageInput');
            const message = input.value.trim();
            
            if (message && customerWs && customerWs.readyState === WebSocket.OPEN) {
                const messageData = {
                    type: 'text',
                    content: message
                };
                
                customerWs.send(JSON.stringify(messageData));
                addCustomerMessage('customer', `You: ${message}`);
                input.value = '';
            }
        }

        function addCustomerMessage(type, content) {
            const messages = document.getElementById('customerMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.textContent = content;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // Enter key support
        document.getElementById('userMessageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendUserMessage();
            }
        });

        document.getElementById('customerMessageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendCustomerMessage();
            }
        });

        // Check authentication on page load
        getCurrentUser();
    </script>
</body>
</html>
