import typer
import asyncio
from typing_extensions import Annotated
import casbin

from app.db.session import AsyncSessionLocal
from app.crud import crud_user, crud_organization, crud_team
from app.crud.crud_role import crud_role

from app.schemas.user import UserCreate
from app.schemas.organization import OrganizationCreate, OrganizationUpdate
from app.schemas.team import TeamCreate
from app.schemas.role import RoleCreate
from app.core.permissions import get_enforcer
from app.core.security import get_password_hash
import getpass

cli = typer.Typer()

async def seed_permissions():
    """
    Seeds the database with the default Casbin policies.
    This is idempotent - it won't create duplicate policies.
    """
    enforcer = get_enforcer()
    print("🌱 Seeding default Casbin policies...")

    policies = [
        # SuperAdmin: Ultimate access - can do anything anywhere
        ["SuperAdmin", "*", "*"],
        ["SuperAdmin", "company:*", "*"],
        ["SuperAdmin", "admin", "*"],
        ["SuperAdmin", "users", "*"],
        ["SuperAdmin", "roles", "*"],
        ["SuperAdmin", "organizations", "*"],
        ["SuperAdmin", "teams", "*"],
        ["SuperAdmin", "conversations", "*"],
        ["SuperAdmin", "messages", "*"],
        ["SuperAdmin", "customers", "*"],
        ["SuperAdmin", "notes", "*"],
        ["SuperAdmin", "labels", "*"],
        ["SuperAdmin", "canned_responses", "*"],
        ["SuperAdmin", "media", "*"],

        # Admin: Can do anything (*) on any resource in any company
        ["Admin", "company:*", "*"],
        ["Admin", "admin", "*"],
        ["Admin", "users", "*"],
        ["Admin", "roles", "*"],
        ["Admin", "organizations", "*"],
        ["Admin", "teams", "*"],
        ["Admin", "conversations", "*"],
        ["Admin", "messages", "*"],
        ["Admin", "customers", "*"],
        ["Admin", "notes", "*"],
        ["Admin", "labels", "*"],
        ["Admin", "canned_responses", "*"],
        ["Admin", "media", "*"],

        # Manager: Can read all major resources in their company
        ["Manager", "company:*:conversations", "read"],
        ["Manager", "company:*:messages", "read"],
        ["Manager", "company:*:users", "read"],
        ["Manager", "company:*:teams", "read"],
        ["Manager", "company:*:roles", "read"],
        ["Manager", "company:*:customers", "read"],
        ["Manager", "company:*:notes", "read"],
        ["Manager", "company:*:labels", "read"],
        ["Manager", "company:*:canned_responses", "read"],
        # Manager: Can also manage users and teams
        ["Manager", "company:*:users", "create"],
        ["Manager", "company:*:users", "update"],
        ["Manager", "company:*:teams", "create"],
        ["Manager", "company:*:teams", "update"],
        ["Manager", "company:*:customers", "create"],
        ["Manager", "company:*:customers", "update"],
        ["Manager", "company:*:notes", "create"],
        ["Manager", "company:*:notes", "update"],
        ["Manager", "company:*:labels", "create"],
        ["Manager", "company:*:labels", "update"],

        # Agent: Limited permissions
        ["Agent", "conversations", "read:own"],
        ["Agent", "conversations", "read:team"],
        ["Agent", "conversations", "create"],
        ["Agent", "conversations", "update:own"],
        ["Agent", "messages", "create"],
        ["Agent", "messages", "read:own"],
        ["Agent", "messages", "read:team"],
        ["Agent", "messages", "delete:own"],
        ["Agent", "users", "read:own"],
        ["Agent", "users", "update:own"],
        ["Agent", "customers", "read:own"],
        ["Agent", "customers", "read:team"],
        ["Agent", "customers", "create"],
        ["Agent", "customers", "update:own"],
        ["Agent", "notes", "create"],
        ["Agent", "notes", "read:own"],
        ["Agent", "notes", "update:own"],
        ["Agent", "canned_responses", "read"],
        ["Agent", "canned_responses", "create"],
        ["Agent", "canned_responses", "update:own"],
        ["Agent", "media", "upload"],
        ["Agent", "media", "read"],
    ]

    # --- FIX 1: REMOVED `await` ---
    # The enforcer's add_policies method is synchronous.
    enforcer.add_policies(policies)

    print("✅ Casbin policies seeded/verified.")

async def create_default_entities(org_name: str, team_name: str):
    """Creates the default organization and team if they don't exist."""
    async with AsyncSessionLocal() as db:
        org = await crud_organization.get_organization_by_name(db, name=org_name)
        if not org:
            org = await crud_organization.create_organization(db, OrganizationCreate(
                name=org_name, description=f"The primary organization for {org_name}.", is_active=True
            ))
            print(f"✅ Organization '{org.name}' created.")
        else:
            print(f"ℹ️  Organization '{org.name}' already exists.")

        team = await crud_team.get_team_by_name(db, name=team_name, organization_id=org.id)
        if not team:
            team = await crud_team.create_team(db, TeamCreate(
                name=team_name, description=f"The default team for {org_name}.", organization_id=org.id, is_active=True
            ))
            print(f"✅ Team '{team.name}' created.")
        else:
            print(f"ℹ️  Team '{team.name}' already exists.")
            
        if not org.default_team_id:
            await crud_organization.update_organization(db, org.id, OrganizationUpdate(default_team_id=team.id))
            print(f"✅ Set '{team.name}' as the default team for '{org.name}'.")

async def create_admin_user(full_name: str, email: str, password: str, org_name: str):
    """Creates the admin user and necessary roles, then assigns the user to the Admin role."""
    async with AsyncSessionLocal() as db:
        try:
            org = await crud_organization.get_organization_by_name(db=db, name=org_name)
            if not org:
                print(f"❌ Organization '{org_name}' not found. Cannot create admin user.")
                return

            team = await crud_team.get_team_by_name(db, name="Default Team", organization_id=org.id)
            if not team:
                # If default team name was changed, try to get the first team
                teams = await crud_team.get_teams_by_organization(db, organization_id=org.id, limit=1)
                if not teams:
                    print(f"❌ No teams found in '{org_name}'. Cannot assign user to team.")
                    return
                team = teams[0]
                print(f"⚠️ 'Default Team' not found. Assigning user to first available team: '{team.name}'.")

            # Ensure system roles exist for this organization
            for role_name in ["SuperAdmin", "Admin", "Agent", "Manager"]:
                role = await crud_role.get_role_by_name(db, role_name, company_id=org.id)
                if not role:
                    await crud_role.create_role(db, RoleCreate(
                        name=role_name, description=f"System role: {role_name}", is_system_role=True, company_id=org.id
                    ))
                    print(f"✅ Role '{role_name}' created for organization '{org.name}'.")

            existing_user = await crud_user.get_user_by_email(db=db, email=email)
            if existing_user:
                print(f"⚠️ User '{email}' already exists. Skipping creation.")
                admin_user = existing_user
            else:
                admin_data = UserCreate(
                    email=email, password=password, full_name=full_name, company_id=org.id, team_id=team.id
                )
                admin_user = await crud_user.create_user(db, admin_data)
                print(f"✅ Admin user '{email}' created and assigned to team '{team.name}'.")
            
            enforcer = get_enforcer()
            casbin_user_subject = str(admin_user.id)
            
            # --- FIX 2: REMOVED `await` ---
            # The enforcer's add_grouping_policy method is also synchronous.
            if not enforcer.has_grouping_policy(casbin_user_subject, "Admin"):
                enforcer.add_grouping_policy(casbin_user_subject, "Admin")
                print(f"✅ Casbin grouping policy added: User '{admin_user.id}' is an 'Admin'.")

        except Exception as e:
            print(f"❌ Error creating admin user: {e}")
            import traceback
            traceback.print_exc()

async def async_setup_defaults(org_name, team_name, full_name, email, password):
    """The main async function to run all setup tasks in order."""
    print("🚀 Setting up default data...")
    # 1. Create the base entities
    await create_default_entities(org_name, team_name)
    # 2. Seed the permission policies
    await seed_permissions()
    # 3. Create the admin user and assign them to the admin role
    await create_admin_user(full_name, email, password, org_name)
    print("\n🎉 Default data setup complete!")
    print(f"👉 Admin user '{email}' created with the password you entered.")

@cli.command()
def setup_defaults(
    org_name: Annotated[str, typer.Option(prompt="Organization Name")] = "Yupcha Inc.",
    team_name: Annotated[str, typer.Option(prompt="Default Team Name")] = "Default Team",
    full_name: Annotated[str, typer.Option(prompt="Admin Full Name")] = "Admin User",
    email: Annotated[str, typer.Option(prompt="Admin Email")] = "<EMAIL>",
    password: Annotated[str, typer.Option(prompt="Admin Password", hide_input=True, confirmation_prompt=True)] = "adminpassword"
):
    """
    Initializes the application with a default organization, team, admin user, and permissions.
    This command is idempotent and safe to run multiple times.
    """
    asyncio.run(async_setup_defaults(org_name, team_name, full_name, email, password))

async def create_super_admin():
    """
    Interactive function to create a SuperAdmin user with unlimited permissions.
    """
    print("🔐 Creating SuperAdmin User")
    print("=" * 50)

    # Get user input
    email = input("Enter SuperAdmin email: ").strip()
    if not email:
        print("❌ Email is required!")
        return

    full_name = input("Enter SuperAdmin full name: ").strip()
    if not full_name:
        print("❌ Full name is required!")
        return

    # Get password securely
    password = getpass.getpass("Enter SuperAdmin password: ")
    if len(password) < 8:
        print("❌ Password must be at least 8 characters long!")
        return

    confirm_password = getpass.getpass("Confirm password: ")
    if password != confirm_password:
        print("❌ Passwords do not match!")
        return

    async with AsyncSessionLocal() as db:
        try:
            # Check if user already exists
            existing_user = await crud_user.get_user_by_email(db, email=email)
            if existing_user:
                print(f"❌ User with email '{email}' already exists!")

                # Ask if they want to upgrade to SuperAdmin
                upgrade = input("Do you want to upgrade this user to SuperAdmin? (y/N): ").strip().lower()
                if upgrade in ['y', 'yes']:
                    # Find or create SuperAdmin role
                    super_admin_role = await crud_role.get_role_by_name(db, "SuperAdmin", company_id=existing_user.organization_id)
                    if not super_admin_role:
                        # Create SuperAdmin role for the user's organization
                        super_admin_role = await crud_role.create_role(db, RoleCreate(
                            name="SuperAdmin",
                            description="Super Administrator with unlimited permissions",
                            is_system_role=True,
                            company_id=existing_user.organization_id
                        ))
                        print("✅ SuperAdmin role created.")

                    # Update user's role
                    existing_user.role_id = super_admin_role.id
                    await db.commit()
                    await db.refresh(existing_user)

                    print(f"✅ User '{email}' upgraded to SuperAdmin!")
                    return
                else:
                    print("❌ Operation cancelled.")
                    return

            # Get or create default organization
            org = await crud_organization.get_organization_by_name(db, name="Default Organization")
            if not org:
                org = await crud_organization.create_organization(db, OrganizationCreate(
                    name="Default Organization",
                    description="Default organization for SuperAdmin"
                ))
                print("✅ Default organization created.")

            # Get or create default team
            teams = await crud_team.get_teams_by_organization(db, organization_id=org.id, limit=1)
            if not teams:
                team = await crud_team.create_team(db, TeamCreate(
                    name="SuperAdmin Team",
                    description="Default team for SuperAdmin",
                    organization_id=org.id
                ))
                print("✅ SuperAdmin team created.")
            else:
                team = teams[0]

            # Create or get SuperAdmin role
            super_admin_role = await crud_role.get_role_by_name(db, "SuperAdmin", company_id=org.id)
            if not super_admin_role:
                super_admin_role = await crud_role.create_role(db, RoleCreate(
                    name="SuperAdmin",
                    description="Super Administrator with unlimited permissions",
                    is_system_role=True,
                    company_id=org.id
                ))
                print("✅ SuperAdmin role created.")

            # Create the SuperAdmin user
            hashed_password = get_password_hash(password)
            user_data = UserCreate(
                email=email,
                full_name=full_name,
                password=password,  # This will be hashed by the crud function
                organization_id=org.id,
                team_id=team.id,
                role_id=super_admin_role.id
            )

            new_user = await crud_user.create_user(db=db, user_in=user_data)

            print("\n🎉 SuperAdmin user created successfully!")
            print(f"📧 Email: {new_user.email}")
            print(f"👤 Name: {new_user.full_name}")
            print(f"🏢 Organization: {org.name}")
            print(f"👥 Team: {team.name}")
            print(f"🔑 Role: {super_admin_role.name}")
            print(f"🆔 User ID: {new_user.id}")

            # Seed permissions to ensure SuperAdmin has all permissions
            await seed_permissions()

            print("\n✅ SuperAdmin setup complete! You can now login with unlimited permissions.")

        except Exception as e:
            print(f"❌ Error creating SuperAdmin: {e}")
            import traceback
            traceback.print_exc()

@cli.command()
def create_superadmin():
    """Create a SuperAdmin user with unlimited permissions."""
    asyncio.run(create_super_admin())

@cli.command()
def seed_perms():
    """Seed default Casbin permissions."""
    asyncio.run(seed_permissions())

if __name__ == "__main__":
    cli()
