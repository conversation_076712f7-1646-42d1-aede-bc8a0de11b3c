#!/bin/bash

# Comprehensive API testing script using curl for UUID-based routes
BASE_URL="http://localhost:8000"
COOKIE_JAR="/tmp/api_cookies.txt"

echo "🚀 Starting comprehensive API tests with curl for UUID-based routes"
echo "Using cookie jar: $COOKIE_JAR"

# Clean up any existing cookies
rm -f $COOKIE_JAR

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=${4:-200}
    
    echo -e "\n${BLUE}🔍 Testing $method $endpoint${NC}"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" -b $COOKIE_JAR "$BASE_URL$endpoint")
    elif [ "$method" = "POST" ] && [ -n "$data" ]; then
        if [[ "$endpoint" == *"/auth/login"* ]]; then
            # Use form data for login
            response=$(curl -s -w "HTTPSTATUS:%{http_code}" -c $COOKIE_JAR -d "$data" "$BASE_URL$endpoint")
        else
            # Use JSON for other POST requests
            response=$(curl -s -w "HTTPSTATUS:%{http_code}" -b $COOKIE_JAR -H "Content-Type: application/json" -d "$data" "$BASE_URL$endpoint")
        fi
    else
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" -b $COOKIE_JAR -X $method "$BASE_URL$endpoint")
    fi
    
    # Extract HTTP status code
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    # Extract response body
    body=$(echo $response | sed -e 's/HTTPSTATUS:.*//g')
    
    echo "   Status: $http_code"
    
    if [ "$http_code" -eq "$expected_status" ]; then
        echo -e "   ${GREEN}✅ Success${NC}"
        if [ -n "$body" ] && [ "$body" != "null" ]; then
            echo "   Response: $body" | head -c 200
            if [ ${#body} -gt 200 ]; then
                echo "..."
            fi
            echo
        fi
    else
        echo -e "   ${RED}❌ Expected $expected_status, got $http_code${NC}"
        if [ -n "$body" ]; then
            echo "   Error: $body"
        fi
    fi
    
    # Return the response body for further processing
    echo "$body"
}

echo -e "\n${YELLOW}=================================================="
echo "1. HEALTH CHECK"
echo -e "==================================================${NC}"

health_response=$(test_endpoint "GET" "/health")

echo -e "\n${YELLOW}=================================================="
echo "2. AUTHENTICATION"
echo -e "==================================================${NC}"

# Login with form data
login_data="username=<EMAIL>&password=adminpassword"
login_response=$(test_endpoint "POST" "/api/v1/auth/login" "$login_data")

# Get current user info
me_response=$(test_endpoint "GET" "/api/v1/auth/me")

# Extract user info for later use
USER_ID=$(echo "$me_response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
ORG_ID=$(echo "$me_response" | grep -o '"company_id":"[^"]*"' | cut -d'"' -f4)
TEAM_ID=$(echo "$me_response" | grep -o '"team_id":"[^"]*"' | cut -d'"' -f4)

echo "   👤 User ID: $USER_ID"
echo "   🏢 Org ID: $ORG_ID"
echo "   👥 Team ID: $TEAM_ID"

echo -e "\n${YELLOW}=================================================="
echo "3. ORGANIZATIONS"
echo -e "==================================================${NC}"

orgs_response=$(test_endpoint "GET" "/api/v1/organizations")

if [ -n "$ORG_ID" ]; then
    org_detail=$(test_endpoint "GET" "/api/v1/organizations/$ORG_ID")
fi

echo -e "\n${YELLOW}=================================================="
echo "4. TEAMS"
echo -e "==================================================${NC}"

teams_response=$(test_endpoint "GET" "/api/v1/teams")

if [ -n "$TEAM_ID" ]; then
    team_detail=$(test_endpoint "GET" "/api/v1/teams/$TEAM_ID")
fi

echo -e "\n${YELLOW}=================================================="
echo "5. USERS"
echo -e "==================================================${NC}"

users_response=$(test_endpoint "GET" "/api/v1/users")

if [ -n "$USER_ID" ]; then
    user_detail=$(test_endpoint "GET" "/api/v1/users/$USER_ID")
fi

echo -e "\n${YELLOW}=================================================="
echo "6. ROLES"
echo -e "==================================================${NC}"

roles_response=$(test_endpoint "GET" "/api/v1/roles")

echo -e "\n${YELLOW}=================================================="
echo "7. CUSTOMERS"
echo -e "==================================================${NC}"

customers_response=$(test_endpoint "GET" "/api/v1/customers")

# Create a test customer
if [ -n "$ORG_ID" ]; then
    customer_data="{\"customer_id\":\"test-customer-001\",\"name\":\"Test Customer\",\"email\":\"<EMAIL>\",\"phone\":\"+1234567890\",\"organization_id\":\"$ORG_ID\"}"
    new_customer=$(test_endpoint "POST" "/api/v1/customers" "$customer_data" 201)
    
    # Extract customer ID
    CUSTOMER_ID=$(echo "$new_customer" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    echo "   🛒 Created customer ID: $CUSTOMER_ID"
    
    if [ -n "$CUSTOMER_ID" ]; then
        customer_detail=$(test_endpoint "GET" "/api/v1/customers/$CUSTOMER_ID")
    fi
fi

echo -e "\n${YELLOW}=================================================="
echo "8. CONVERSATIONS"
echo -e "==================================================${NC}"

conversations_response=$(test_endpoint "GET" "/api/v1/conversations")

# Create a test conversation
if [ -n "$CUSTOMER_ID" ] && [ -n "$ORG_ID" ]; then
    conversation_data="{\"customer_id\":\"$CUSTOMER_ID\",\"organization_id\":\"$ORG_ID\",\"status\":\"new\"}"
    new_conversation=$(test_endpoint "POST" "/api/v1/conversations" "$conversation_data" 201)
    
    # Extract conversation ID
    CONVERSATION_ID=$(echo "$new_conversation" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    echo "   💬 Created conversation ID: $CONVERSATION_ID"
    
    if [ -n "$CONVERSATION_ID" ]; then
        conversation_detail=$(test_endpoint "GET" "/api/v1/conversations/$CONVERSATION_ID")
    fi
fi

echo -e "\n${YELLOW}=================================================="
echo "9. CANNED RESPONSES"
echo -e "==================================================${NC}"

canned_responses=$(test_endpoint "GET" "/api/v1/canned-responses")

# Create a test canned response
if [ -n "$ORG_ID" ] && [ -n "$USER_ID" ]; then
    canned_data="{\"title\":\"Test Response\",\"content\":\"This is a test canned response\",\"shortcut\":\"test\",\"category\":\"greeting\",\"organization_id\":\"$ORG_ID\",\"created_by\":\"$USER_ID\",\"is_active\":true,\"is_public\":true}"
    new_canned=$(test_endpoint "POST" "/api/v1/canned-responses" "$canned_data" 201)
    
    # Extract canned response ID
    CANNED_ID=$(echo "$new_canned" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    echo "   📋 Created canned response ID: $CANNED_ID"
    
    if [ -n "$CANNED_ID" ]; then
        canned_detail=$(test_endpoint "GET" "/api/v1/canned-responses/$CANNED_ID")
    fi
fi

echo -e "\n${YELLOW}=================================================="
echo "✅ API TESTING COMPLETED"
echo -e "==================================================${NC}"
echo "All UUID-based routes have been tested with curl!"

# Clean up
rm -f $COOKIE_JAR

echo -e "\n${GREEN}🎉 Testing completed successfully!${NC}"
