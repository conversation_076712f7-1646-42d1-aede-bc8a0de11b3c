# WebSocket Chat Testing Guide

This guide explains how to test the real-time WebSocket chat functionality using the provided test scripts.

## Overview

We have two test scripts:
1. **CU_TEST.py** - Simulates a customer connecting to the chat
2. **AG_TEST.py** - Simulates an agent connecting to the chat

These scripts allow you to test the full WebSocket communication flow between customers and agents.

## Prerequisites

- Python 3.8+ installed
- Required packages: `websockets`, `aiohttp`, `aioconsole`
- Server running on port 8000

## Installation

Install the required packages:

```bash
pip install websockets aiohttp aioconsole
```

## Testing Steps

### Step 1: Start the Server

Make sure your server is running on port 8000:

```bash
cd /home/<USER>/Documents/Devlopment/yupcha-customerbot-ai
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Step 2: Start the Customer Client

In a new terminal window, run:

```bash
python CU_TEST.py
```

This will:
1. Create a new customer
2. Create a new conversation
3. Connect to the WebSocket for that conversation
4. Allow you to send messages as the customer

You should see output like:

```
Setting up a new customer and conversation...
✅ Setup complete. Conversation ID: 06874e73-0b6b-766d-8000-12dfb3637338

==================================================
🚀 Yupcha Interactive Customer Chat Client
👤 Customer ID: f7b03d72-7282-4080-b932-646483e88b32
💬 Conversation ID: 06874e73-0b6b-766d-8000-12dfb3637338
--------------------------------------------------
Type a message and press Enter. Type 'quit' to exit.
==================================================

>> Your message: 
```

**Important:** Note the Conversation ID displayed in the output. You'll need this for the agent client.

### Step 3: Send a Test Message as Customer

In the customer client, type a message and press Enter:

```
>> Your message: Hello, I need help with my order #12345
```

### Step 4: Start the Agent Client

In another terminal window, run:

```bash
python AG_TEST.py
```

This will:
1. Log in as an agent (using <EMAIL> / adminpassword)
2. Prompt you to enter the conversation ID
3. Connect to the WebSocket for that conversation
4. Allow you to send messages as the agent

You should see:

```
Logging in as agent...
✅ Agent login successful
Enter conversation ID to join: 
```

### Step 5: Enter the Conversation ID

Copy the conversation ID from the customer client and paste it when prompted:

```
Enter conversation ID to join: 06874e73-0b6b-766d-8000-12dfb3637338
```

You should now be connected to the conversation:

```
==================================================
🚀 Yupcha Interactive Agent Chat Client
👤 Agent: <EMAIL>
💬 Conversation ID: 06874e73-0b6b-766d-8000-12dfb3637338
--------------------------------------------------
Type a message and press Enter. Type 'quit' to exit.
==================================================

✅ Connected to conversation as agent
>> Your reply: 
```

### Step 6: Test Two-Way Communication

1. **As the agent**, send a reply:
   ```
   >> Your reply: Hello! I'm here to help with your order #12345. What seems to be the issue?
   ```

2. **As the customer**, you should see the agent's message appear:
   ```
   >> [AGENT]: Hello! I'm here to help with your order #12345. What seems to be the issue?
   ```

3. **As the customer**, send a response:
   ```
   >> Your message: I haven't received my order yet and it's been 7 days
   ```

4. **As the agent**, you should see the customer's message:
   ```
   >> [CUSTOMER]: I haven't received my order yet and it's been 7 days
   ```

5. Continue the conversation back and forth to test real-time communication

### Step 7: Test Disconnection

1. In either client, type `quit` and press Enter to disconnect
2. Observe that the connection is closed properly
3. You can restart the clients to reconnect to the same conversation

## Testing Archive Functionality

After testing the basic chat functionality, you can test the archive feature:

1. Use the API directly to archive a conversation:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/conversations/YOUR_CONVERSATION_ID/archive" -b test_cookies.txt
   ```

2. Use the API to verify the conversation is archived:
   ```bash
   curl -X GET "http://localhost:8000/api/v1/conversations/?include_archived=true" -b test_cookies.txt
   ```

3. Use the API to unarchive the conversation:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/conversations/YOUR_CONVERSATION_ID/unarchive" -b test_cookies.txt
   ```

## Troubleshooting

### Connection Issues

- **WebSocket connection fails**: Verify the server is running on port 8000
- **Authentication fails**: Check the agent credentials in AG_TEST.py
- **Conversation not found**: Ensure you're using the correct conversation ID

### Message Delivery Issues

- **Messages not appearing**: Check server logs for WebSocket errors
- **Delayed messages**: Check network latency and server performance

### Other Issues

- **Script errors**: Ensure all required packages are installed
- **Permission errors**: Verify the agent has access to the conversation

## Next Steps

After successfully testing the WebSocket functionality, you can:

1. Integrate the chat into your frontend application
2. Add more features like typing indicators, read receipts, etc.
3. Implement file sharing and other advanced features
