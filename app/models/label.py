from sqlalchemy import Column, String, ForeignKey, DateTime, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from uuid_extensions import uuid7
from app.db.base import Base
from .associations import conversation_label_association

class Label(Base):
    __tablename__ = "labels"

    id = Column(UUID, primary_key=True, default=uuid7, index=True)
    name = Column(String(50), nullable=False, index=True)
    description = Column(String(255), nullable=True)
    color = Column(String(7), nullable=False, default="#475569")
    
    # Labels are scoped to an organization
    organization_id = Column(UUID, ForeignKey("organizations.id"), nullable=False, index=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Ensure a label name is unique within an organization
    __table_args__ = (UniqueConstraint('name', 'organization_id', name='_organization_label_name_uc'),)

    # Relationships
    organization = relationship("Organization", back_populates="labels")
    conversations = relationship(
        "Conversation",
        secondary=conversation_label_association,
        back_populates="labels"
    )
