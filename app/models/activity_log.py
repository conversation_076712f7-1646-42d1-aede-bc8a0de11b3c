from sqlalchemy import Column, String, DateTime, Text, ForeignKey, JSO<PERSON>
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from uuid_extensions import uuid7
from app.db.base import Base

class ActivityLog(Base):
    __tablename__ = "activity_logs"

    id = Column(UUID, primary_key=True, default=uuid7, index=True)

    user_id = Column(UUID, ForeignKey("users.id"), nullable=True, index=True)
    user_email = Column(String, nullable=True) 

    action = Column(String(255), nullable=False, index=True)
    status = Column(String(50), nullable=False, default="success")

    target_resource_type = Column(String(100), nullable=True, index=True)  
    target_resource_id = Column(UUID, nullable=True, index=True)
    
    details = Column(JSON, nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship
    user = relationship("User")
