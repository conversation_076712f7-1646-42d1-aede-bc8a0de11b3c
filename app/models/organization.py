from sqlalchemy import Column, String, DateTime, Text, Boolean, ForeignKey, select, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.sql import func
from uuid_extensions import uuid7

from app.db.base import Base

class Organization(Base):
    __tablename__ = "organizations"

    id = Column(UUID, primary_key=True, default=uuid7, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    website = Column(String(255), nullable=True)
    email = Column(String(255), nullable=True)
    phone = Column(String(50), nullable=True)
    address = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    default_team_id = Column(UUID, ForeignKey("teams.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    users = relationship("User", back_populates="organization")
    teams = relationship("Team", back_populates="organization", foreign_keys="Team.organization_id", cascade="all, delete")
    conversations = relationship("Conversation", back_populates="organization", cascade="all, delete")
    canned_responses = relationship("CannedResponse", back_populates="organization", cascade="all, delete")  # NEW
    labels = relationship("Label", back_populates="organization", cascade="all, delete-orphan")
    default_team = relationship("Team", foreign_keys=[default_team_id], post_update=True)

    @hybrid_property
    def teams_count(self):
        return len(self.teams)

    @teams_count.expression
    def teams_count(cls):
        from app.models.team import Team #* Import here to avoid circular dependency
        return select(func.count(Team.id)).where(Team.organization_id == cls.id).label("teams_count")

    @hybrid_property
    def users_count(self):
        return len(self.users)

    @users_count.expression
    def users_count(cls):
        from app.models.user import User #* Import here to avoid circular dependency
        return select(func.count(User.id)).where(User.organization_id == cls.id).label("users_count")
