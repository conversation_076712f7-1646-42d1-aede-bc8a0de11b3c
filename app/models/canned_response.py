from sqlalchemy import Column, Integer, String, Text, Boolean, ForeignKey, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from uuid_extensions import uuid7

from app.db.base import Base

class CannedResponse(Base):
    __tablename__ = "canned_responses"

    id = Column(UUID, primary_key=True, default=uuid7, index=True)
    title = Column(String(255), nullable=False, index=True)
    content = Column(Text, nullable=False)
    shortcut = Column(String(50), nullable=True, index=True)  # Quick access shortcut like "/hello"
    category = Column(String(100), nullable=True, index=True)  # e.g., "greeting", "closing", "technical"

    # Organization and user relationships
    organization_id = Column(UUID, ForeignKey("organizations.id"), nullable=False, index=True)
    created_by = Column(UUID, ForeignKey("users.id"), nullable=False, index=True)
    
    # Status and metadata
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_public = Column(Boolean, default=True, nullable=False)  # If false, only visible to creator
    usage_count = Column(Integer, default=0, nullable=False)  # Track how often it's used
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_used_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    organization = relationship("Organization", back_populates="canned_responses")
    creator = relationship("User", back_populates="canned_responses")

    def __repr__(self):
        return f"<CannedResponse(id={self.id}, title='{self.title}', shortcut='{self.shortcut}')>"

    def increment_usage(self):
        """Increment usage count and update last used timestamp"""
        self.usage_count += 1
        self.last_used_at = func.now()
