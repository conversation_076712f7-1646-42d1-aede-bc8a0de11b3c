from sqlalchemy import Table, Column, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from app.db.base import Base

user_role_association = Table(
    "user_role_association",
    Base.metadata,
    Column("user_id", UUID, ForeignKey("users.id"), primary_key=True),
    Column("role_id", UUID, ForeignKey("roles.id"), primary_key=True),
)

# NEW: Association table for Conversations and Labels
conversation_label_association = Table(
    'conversation_label_association',
    Base.metadata,
    Column('conversation_id', UUID, ForeignKey('conversations.id'), primary_key=True),
    Column('label_id', UUID, ForeignKey('labels.id'), primary_key=True)
)