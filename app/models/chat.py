import enum
from sqlalchemy import Column, String, Text, DateTime, Foreign<PERSON>ey, Boolean, Enum as SQLAlchemyEnum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from uuid_extensions import uuid7
from app.db.base import Base
from app.models.associations import conversation_label_association

# New Enum for conversation status
class ConversationStatus(str, enum.Enum):
    new = "new"
    open = "open"
    closed = "closed"
    archived = "archived"  # NEW: Add archived status

class Conversation(Base):
    __tablename__ = "conversations"

    id = Column(UUID, primary_key=True, default=uuid7, index=True)
    customer_id = Column(UUID, ForeignKey("customers.id"), nullable=False, index=True)
    organization_id = Column(UUID, ForeignKey("organizations.id"), nullable=False, index=True)
    status = Column(SQLAlchemyEnum(ConversationStatus, name="conversation_status_enum", create_type=True), default=ConversationStatus.new, nullable=False, index=True)

    # This flag controls if the bot can respond in this conversation. Defaults to True.
    is_bot_active = Column(Boolean, default=True, nullable=False, server_default="true")

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Changed from assigned_agent_id to assigned_team_id
    assigned_team_id = Column(UUID, ForeignKey("teams.id"), nullable=True, index=True)

    # Relationships
    customer = relationship("Customer", back_populates="conversations")
    organization = relationship("Organization", back_populates="conversations")
    assigned_team = relationship("Team", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")

    # NEW: Add the many-to-many relationship to labels
    labels = relationship(
        "Label",
        secondary=conversation_label_association,
        back_populates="conversations",
        lazy="selectin" # Eagerly load labels with the conversation
    )

class Message(Base):
    __tablename__ = "messages"

    id = Column(UUID, primary_key=True, default=uuid7, index=True)
    conversation_id = Column(UUID, ForeignKey("conversations.id"), nullable=False, index=True)
    customer_id = Column(UUID, ForeignKey("customers.id"), nullable=True, index=True)  # For customer messages
    user_id = Column(UUID, ForeignKey("users.id"), nullable=True, index=True)  # For agent/admin messages
    content = Column(Text, nullable=True)  # Made nullable for media messages
    sender = Column(String(50), nullable=False)  # 'customer', 'bot', 'agent', 'admin'
    message_type = Column(String(50), default="text")  # text, image, video, audio, gif, document
    asset_id = Column(UUID, ForeignKey("assets.id"), nullable=True, index=True)  # For media messages
    ip_address = Column(String(45), nullable=True)  # Renamed from message_metadata
    location = Column(String(255), nullable=True)  # New location column
    deleted = Column(Boolean, default=False, nullable=False, index=True)  # Boolean field for message deletion
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    read_at = Column(DateTime(timezone=True), nullable=True)
    read_by_user_id = Column(UUID, ForeignKey("users.id"), nullable=True)

    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
    customer = relationship("Customer", back_populates="messages")
    user = relationship("User", back_populates="messages", foreign_keys=[user_id])
    asset = relationship("Asset", back_populates="messages")
    read_by = relationship("User", foreign_keys=[read_by_user_id])
