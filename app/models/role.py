from sqlalchemy import (
    Column, String, Table, ForeignKey, DateTime, Boolean,
    UniqueConstraint
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from uuid_extensions import uuid7
from app.models.associations import user_role_association
from app.db.base import Base

class Role(Base):
    __tablename__ = "roles"

    id = Column(UUID, primary_key=True, default=uuid7, index=True)
    name = Column(String(50), index=True, nullable=False)
    description = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_system_role = Column(Boolean, default=False, nullable=False)
    company_id = Column(UUID, ForeignKey("organizations.id"), nullable=False, index=True)

    __table_args__ = (UniqueConstraint('name', 'company_id', name='_company_role_name_uc'),)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    users = relationship("User", back_populates="role")

    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}')>"
