from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from app.models.chat import Conversation, Message
from app.db.session import get_async_db
from app.models.user import User
from app.auth.dependencies import require_permission
from app.crud import crud_chat
from typing import List

router = APIRouter()

@router.get("/stats", dependencies=[Depends(require_permission("read", "analytics"))])
async def get_analytics_stats(db: AsyncSession = Depends(get_async_db)):
    total_conversations = await db.execute(select(func.count(Conversation.id)))
    open_conversations = await db.execute(select(func.count(Conversation.id)).where(Conversation.status == 'open'))
    # This is a simplified average response time. A more accurate calculation would be needed in a real application.
    avg_response_time = await db.execute(select(func.avg(Message.created_at - Conversation.created_at)).join(Message, Message.conversation_id == Conversation.id))
    return {
        "total_conversations": total_conversations.scalar_one(),
        "open_conversations": open_conversations.scalar_one(),
        "average_response_time": avg_response_time.scalar_one()
    }

@router.get("/agent-performance", dependencies=[Depends(require_permission("read", "analytics"))])
async def get_agent_performance(db: AsyncSession = Depends(get_async_db)):
    result = await db.execute(
        select(User.full_name, func.count(Conversation.id))
        .join(Conversation, Conversation.assigned_to_user_id == User.id)
        .group_by(User.full_name)
    )
    return [{"agent": row[0], "conversations_handled": row[1]} for row in result.all()]
