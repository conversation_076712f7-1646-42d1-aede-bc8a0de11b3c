from fastapi import APIRouter, Depends, status, BackgroundTasks, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from uuid import UUID

from app.db.session import get_async_db
from app.auth.dependencies import get_current_user
from app.models.user import User
from app.schemas.push_subscription import PushSubscriptionCreate, PushSubscriptionResponse, PushNotificationPayload
from app.crud.crud_push_subscription import crud_push_subscription
from app.services.push_notification_service import push_notification_service
from app.core.config import settings

router = APIRouter()

@router.post("/subscribe", response_model=PushSubscriptionResponse, status_code=status.HTTP_201_CREATED)
async def subscribe_to_push_notifications(
    subscription: PushSubscriptionCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Subscribe a user's device to receive push notifications.
    """
    new_sub = await crud_push_subscription.create_for_user(
        db, obj_in=subscription, user_id=current_user.id
    )
    
    # Send a welcome push notification in the background
    welcome_payload = push_notification_service.create_notification_payload(
        title="Yupcha Notifications Enabled",
        body="You're all set to receive real-time updates!",
        icon="/icon.png",
        data={"type": "welcome", "url": "/dashboard"}
    )
    
    background_tasks.add_task(
        push_notification_service.send_push_notification_to_user,
        db,
        user_id=current_user.id,
        payload=welcome_payload
    )

    return new_sub

@router.delete("/unsubscribe/{subscription_id}", status_code=status.HTTP_204_NO_CONTENT)
async def unsubscribe_from_push_notifications(
    subscription_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Unsubscribe a specific device from push notifications.
    """
    deleted_count = await crud_push_subscription.remove_by_id(
        db, subscription_id=subscription_id, user_id=current_user.id
    )
    
    if deleted_count == 0:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscription not found"
        )

@router.delete("/unsubscribe", status_code=status.HTTP_204_NO_CONTENT)
async def unsubscribe_by_endpoint(
    endpoint: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Unsubscribe a device by endpoint URL.
    """
    deleted_count = await crud_push_subscription.remove_by_endpoint(
        db, endpoint=endpoint, user_id=current_user.id
    )
    
    if deleted_count == 0:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscription not found"
        )

@router.get("/subscriptions", response_model=List[PushSubscriptionResponse])
async def get_user_subscriptions(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Get all push subscriptions for the current user.
    """
    subscriptions = await crud_push_subscription.get_by_user_id(db, user_id=current_user.id)
    return subscriptions

@router.get("/vapid-public-key")
async def get_vapid_public_key():
    """
    Get the VAPID public key for frontend subscription.
    This endpoint is public as the public key is meant to be shared.
    """
    return {"publicKey": settings.VAPID_PUBLIC_KEY}

@router.post("/test", status_code=status.HTTP_200_OK)
async def send_test_notification(
    payload: PushNotificationPayload,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Send a test push notification to the current user.
    Useful for testing the push notification system.
    """
    notification_payload = payload.dict()
    
    background_tasks.add_task(
        push_notification_service.send_push_notification_to_user,
        db,
        user_id=current_user.id,
        payload=notification_payload
    )
    
    return {"message": "Test notification sent"}
