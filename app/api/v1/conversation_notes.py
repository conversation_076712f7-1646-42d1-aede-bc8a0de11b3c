from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from uuid import UUID

from app.db.session import get_async_db
from app.crud import crud_conversation_note, crud_chat # Added crud_chat
from app.schemas.conversation_note import ConversationNote, ConversationNoteCreate, ConversationNoteUpdate
from app.models.user import User
from app.auth.dependencies import require_permission

router = APIRouter()

@router.post("/", response_model=ConversationNote, status_code=status.HTTP_201_CREATED, dependencies=[Depends(require_permission("create", "conversation_notes"))])
async def create_conversation_note(
    request: Request,
    note_in: ConversationNoteCreate,
    db: AsyncSession = Depends(get_async_db),
):
    # Verify the conversation exists and belongs to user's organization
    conversation = await crud_chat.get_conversation(db, id=note_in.conversation_id)
    if not conversation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found")

    current_user: User = request.state.user
    if conversation.organization_id != current_user.organization_id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found")

    return await crud_conversation_note.create_conversation_note(db=db, note_in=note_in)

@router.get("/conversation/{conversation_id}", response_model=List[ConversationNote], dependencies=[Depends(require_permission("read", "conversations"))])
async def get_conversation_notes_by_conversation(
    request: Request,
    conversation_id: UUID,
    db: AsyncSession = Depends(get_async_db),
    skip: int = 0,
    limit: int = 100,
):
    # The conversation was already fetched and validated by the dependency
    conversation = request.state.resource

    return await crud_conversation_note.get_conversation_notes_by_conversation(
        db=db, conversation_id=conversation.id, skip=skip, limit=limit
    )

@router.put("/{note_id}", response_model=ConversationNote, dependencies=[Depends(require_permission("update", "conversation_notes"))])
async def update_conversation_note(
    request: Request,
    note_id: UUID,
    note_in: ConversationNoteUpdate,
    db: AsyncSession = Depends(get_async_db),
):
    # The note was already fetched and validated by the dependency
    note = request.state.resource
    return await crud_conversation_note.update_conversation_note(db=db, note=note, note_in=note_in)

@router.delete("/{note_id}", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(require_permission("delete", "conversation_notes"))])
async def delete_conversation_note(
    request: Request,
    note_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    # The note was already fetched and validated by the dependency
    note = request.state.resource
    await crud_conversation_note.delete_conversation_note(db=db, note=note)
    return None