from fastapi import APIRouter, Depends, HTTPException, Request, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from uuid import UUID
from fastapi_cache.decorator import cache

from app.db.session import get_async_db
from app.crud.crud_canned_response import crud_canned_response, get_canned_responses_query
from app.schemas.canned_response import (
    CannedResponseCreate, CannedResponseUpdate, CannedResponseResponse, 
    CannedResponseWithCreator, CannedResponseSearch, CannedResponseStats,
    CannedResponsesByCategory, BulkCannedResponseCreate
)
from app.models.user import User
from app.auth.dependencies import get_current_user, require_permission
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate as async_paginate

router = APIRouter()

@router.get("/", response_model=Page[CannedResponseResponse], dependencies=[Depends(require_permission("read"))])
async def list_canned_responses(
    request: Request,
    category: Optional[str] = Query(None),
    query: Optional[str] = Query(None, description="Search in title and content"),
    shortcut: Optional[str] = Query(None),
    is_public: Optional[bool] = Query(None),
    created_by: Optional[UUID] = Query(None),
    active_only: bool = Query(True),
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """List canned responses for the current user's organization with pagination."""
    if not current_user.organization_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User must belong to an organization"
        )
    
    search = CannedResponseSearch(
        query=query,
        category=category,
        created_by=created_by,
        is_public=is_public,
        shortcut=shortcut
    )
    
    query = get_canned_responses_query(
        organization_id=current_user.organization_id,
        user_id=current_user.id,
        search=search,
        active_only=active_only
    )
    return await async_paginate(db, query)

@router.get("/stats", response_model=CannedResponseStats, dependencies=[Depends(require_permission("read"))])
@cache(expire=300)
async def get_canned_response_stats(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get canned response statistics for the organization"""
    if not current_user.organization_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User must belong to an organization"
        )

    stats = await crud_canned_response.get_stats(
        db, current_user.organization_id, current_user.id
    )
    return stats

@router.get("/categories", response_model=List[str], dependencies=[Depends(require_permission("read"))])
@cache(expire=300)
async def list_categories(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """List all categories in the organization"""
    if not current_user.organization_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User must belong to an organization"
        )

    categories = await crud_canned_response.get_categories(
        db, current_user.organization_id, current_user.id
    )
    return categories

@router.get("/categories/{category}", response_model=CannedResponsesByCategory, dependencies=[Depends(require_permission("read"))])
@cache(expire=300)
async def get_responses_by_category(
    request: Request,
    category: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get all responses in a specific category"""
    if not current_user.organization_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User must belong to an organization"
        )

    responses = await crud_canned_response.get_responses_by_category(
        db, category, current_user.organization_id, current_user.id
    )
    
    return CannedResponsesByCategory(
        category=category,
        responses=responses
    )

@router.get("/popular", response_model=List[CannedResponseResponse], dependencies=[Depends(require_permission("read"))])
@cache(expire=300)
async def get_popular_responses(
    request: Request,
    limit: int = Query(10, ge=1, le=50),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get most popular (most used) canned responses"""
    if not current_user.organization_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User must belong to an organization"
        )

    responses = await crud_canned_response.get_popular_responses(
        db, current_user.organization_id, current_user.id, limit
    )
    return responses

@router.get("/my-responses", response_model=List[CannedResponseResponse], dependencies=[Depends(require_permission("read"))])
@cache(expire=300)
async def get_my_responses(
    request: Request,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get all responses created by the current user"""
    if not current_user.organization_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User must belong to an organization"
        )

    responses = await crud_canned_response.get_user_responses(
        db, current_user.id, current_user.organization_id, skip, limit
    )
    return responses

@router.get("/shortcut/{shortcut}", response_model=CannedResponseResponse, dependencies=[Depends(require_permission("read"))])
@cache(expire=300)
async def get_response_by_shortcut(
    request: Request,
    shortcut: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get a canned response by its shortcut"""
    if not current_user.organization_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User must belong to an organization"
        )

    response = await crud_canned_response.get_canned_response_by_shortcut(
        db, shortcut, current_user.organization_id, current_user.id
    )
    
    if not response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Canned response with shortcut '{shortcut}' not found"
        )
    
    return response

@router.get("/{response_id}", response_model=CannedResponseResponse, dependencies=[Depends(require_permission("read"))])
async def get_canned_response(
    request: Request,
    response_id: UUID,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Get a specific canned response by ID"""
    response = await crud_canned_response.get_canned_response(db, response_id)
    
    if not response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Canned response not found"
        )
    
    return response

@router.post("/", response_model=CannedResponseResponse, status_code=status.HTTP_201_CREATED, dependencies=[Depends(require_permission("create"))])
async def create_canned_response(
    request: Request,
    response: CannedResponseCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new canned response"""
    if not current_user.organization_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User must belong to an organization"
        )
    
    # Check if shortcut already exists (if provided)
    if response.shortcut:
        existing = await crud_canned_response.get_canned_response_by_shortcut(
            db, response.shortcut, current_user.organization_id, current_user.id
        )
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Shortcut '{response.shortcut}' already exists"
            )

    return await crud_canned_response.create_canned_response(
        db, response, current_user.id, current_user.organization_id
    )

@router.post("/bulk", response_model=List[CannedResponseResponse], dependencies=[Depends(require_permission("create"))])
async def create_bulk_canned_responses(
    request: Request,
    bulk_create: BulkCannedResponseCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Create multiple canned responses at once (Admin/Manager only)"""
    if not current_user.organization_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User must belong to an organization"
        )
    
    created_responses = []
    for response_data in bulk_create.responses:
        # Check shortcut conflicts
        if response_data.shortcut:
            existing = await crud_canned_response.get_canned_response_by_shortcut(
                db, response_data.shortcut, current_user.organization_id, current_user.id
            )
            if existing:
                continue  # Skip this one if shortcut exists
        
        created_response = await crud_canned_response.create_canned_response(
            db, response_data, current_user.id, current_user.organization_id
        )
        created_responses.append(created_response)
    
    return created_responses

@router.put("/{response_id}", response_model=CannedResponseResponse, dependencies=[Depends(require_permission("update"))])
async def update_canned_response(
    request: Request,
    response_id: UUID,
    response_update: CannedResponseUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Update a canned response (only creator can update)"""
    # Check if shortcut conflicts (if being updated)
    if response_update.shortcut:
        existing = await crud_canned_response.get_canned_response_by_shortcut(
            db, response_update.shortcut, current_user.organization_id, current_user.id
        )
        if existing and existing.id != response_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Shortcut '{response_update.shortcut}' already exists"
            )
    
    updated_response = await crud_canned_response.update_canned_response(
        db, response_id, response_update, current_user.id
    )
    
    if not updated_response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Canned response not found or not authorized to update"
        )
    
    return updated_response

@router.delete("/{response_id}", status_code=status.HTTP_204_NO_CONTENT, dependencies=[Depends(require_permission("delete"))])
async def delete_canned_response(
    request: Request,
    response_id: UUID,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Delete a canned response (only creator can delete)"""
    success = await crud_canned_response.delete_canned_response(
        db, response_id, current_user.id
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Canned response not found or not authorized to delete"
        )

@router.post("/{response_id}/use", response_model=CannedResponseResponse, dependencies=[Depends(require_permission("update"))])
async def use_canned_response(
    request: Request,
    response_id: UUID,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user),
):
    """Mark a canned response as used (increment usage count)"""
    # First check if user can access this response
    response = await crud_canned_response.get_canned_response(db, response_id)
    
    if not response:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Canned response not found"
        )
    
    # Check access permissions
    if (response.organization_id != current_user.organization_id or
        (not response.is_public and response.created_by != current_user.id)):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to use this canned response"
        )
    
    updated_response = await crud_canned_response.use_canned_response(db, response_id)
    return updated_response
