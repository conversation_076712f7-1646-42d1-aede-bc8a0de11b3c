from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Request, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from uuid import UUID
from fastapi_cache.decorator import cache

from app.db.session import get_async_db
from app.crud import crud_chat, crud_team, crud_label
from app.schemas.ai import SuggestionResponse, SummaryResponse
from app.schemas.chat import ConversationCreate, ConversationResponse, MessageResponse, MessageCreate, MessageCreateRequest, ConversationStatus, ConversationBotToggle
from app.schemas.label import ConversationLabelRequest
from app.models.user import User
from app.services.ai_service import ai_service
from app.services.conversation_service import ConversationService
from app.auth.dependencies import require_permission
from app.services.audit_log_service import audit_log_service
from app.services.realtime_service import broadcast_message
from datetime import datetime, timezone

from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate as async_paginate

import logging

logger = logging.getLogger(__name__)

router = APIRouter()

#* A placeholder function for sending an email
def send_team_notification_email(team_email: str, conversation_id: UUID):
    """Send notification email to team about new conversation"""
    print(f"📧 Sending email to {team_email} about new conversation {conversation_id}")

@router.post("/", response_model=ConversationResponse, dependencies=[Depends(require_permission("create", "conversations"))])
async def create_conversation(
    request: Request,
    conversation: ConversationCreate,
    service: ConversationService = Depends(ConversationService),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    current_user: User = request.state.user
    """
    Create a new conversation.
    The service layer handles auto-assignment and notifications.
    """
    return await service.create_conversation(conversation, background_tasks)


# --- NEW PUBLIC ENDPOINT ---
@router.post("/public/initiate", response_model=ConversationResponse)
async def initiate_public_conversation(
    # We don't use Depends(get_current_user) here, so it's a public endpoint.
    conversation_data: ConversationCreate,
    service: ConversationService = Depends(ConversationService),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    """
    Public endpoint for an unauthenticated customer (e.g., from a website widget)
    to create a new conversation.
    """
    # Here you might want to add some extra security, like a captcha check
    # or a check against a trusted origin header, but for now, this is fine.
    
    # We call the same robust service layer function.
    return await service.create_conversation(conversation_data, background_tasks)

@router.get("/{conversation_id}", response_model=ConversationResponse, dependencies=[Depends(require_permission("read", "conversations"))])
@cache(expire=300)
async def get_conversation(
    request: Request,
    conversation_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """Get a specific conversation by ID"""
    logger.info(f"Attempting to get conversation: {conversation_id}")
    # The conversation was already fetched and validated by the dependency
    conversation = request.state.resource
    logger.info(f"Successfully retrieved conversation: {conversation_id}")
    return conversation

@router.get("/{conversation_id}/messages", response_model=List[MessageResponse], dependencies=[Depends(require_permission("read", "conversations"))])
@cache(expire=300)
async def get_conversation_messages(
    request: Request,
    conversation_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """Get all messages for a specific conversation"""
    logger.info(f"Attempting to get messages for conversation: {conversation_id}")
    # The conversation was already fetched and validated by the dependency
    conversation = request.state.resource
    messages = await crud_chat.get_messages_by_conversation(db=db, conversation_id=conversation.id)
    logger.info(f"Retrieved {len(messages)} messages for conversation: {conversation_id}")
    return messages

@router.get("/", response_model=Page[ConversationResponse], dependencies=[Depends(require_permission("read", "conversations"))])
async def list_conversations(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    status: Optional[ConversationStatus] = Query(None, description="Filter by conversation status."),
    team_id: Optional[UUID] = Query(None, description="Filter by assigned team ID."),
    customer_id: Optional[UUID] = Query(None, description="Filter by customer ID."),
    unassigned: Optional[bool] = Query(False, description="Filter for unassigned conversations."),
    include_archived: bool = Query(False, description="Include archived conversations in results."),
    start_date: Optional[datetime] = Query(None, description="Filter conversations created after this date."),
    end_date: Optional[datetime] = Query(None, description="Filter conversations created before this date."),
    label_ids: Optional[List[UUID]] = Query(None, description="Filter conversations by label IDs."),
    search_query: Optional[str] = Query(None, description="Search for conversations by message content.")
):
    """List conversations with pagination and filtering."""
    current_user: User = request.state.user
    query = crud_chat.get_conversations_query(
        organization_id=current_user.organization_id,
        status=status,
        team_id=team_id,
        customer_id=customer_id,
        unassigned=unassigned,
        include_archived=include_archived,
        start_date=start_date,
        end_date=end_date,
        label_ids=label_ids,
        search_query=search_query
    )
    return await async_paginate(db, query)

@router.post("/{conversation_id}/messages", response_model=MessageResponse, dependencies=[Depends(require_permission("create", "conversations"))])
async def create_message(
    request: Request,
    conversation_id: UUID,
    message: MessageCreateRequest,
    db: AsyncSession = Depends(get_async_db),
):
    """Create a new message in a conversation. Agent or Admin access required."""
    conversation = await crud_chat.get_conversation(db, id=conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    # Create the message with conversation_id
    message_data = MessageCreate(
        conversation_id=conversation_id,
        content=message.content,
        sender=message.sender,
        message_type=message.message_type
    )
    created_message = await crud_chat.create_message(db=db, message=message_data)

    return created_message

@router.post("/{conversation_id}/assign/{team_id}", response_model=ConversationResponse, dependencies=[Depends(require_permission("assign", "conversations"))])
async def assign_team(
    request: Request,
    background_tasks: BackgroundTasks,
    conversation_id: UUID,
    team_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    current_user: User = request.state.user
    """Assign a team to a conversation. Admin access required."""
    team = await crud_team.get_team(db=db, id=team_id)
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")

    conversation = await crud_chat.assign_team_to_conversation(
        db, conversation_id=conversation_id, team_id=team_id
    )
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="conversation.assign_team",
        target_resource=conversation,
        details={"assigned_to_team_id": team_id}
    )
    return conversation

@router.get("/unassigned", response_model=List[ConversationResponse], dependencies=[Depends(require_permission("read", "conversations"))])
async def get_unassigned_conversations(
    request: Request,
    db: AsyncSession = Depends(get_async_db),
):
    """Get unassigned conversations. Agent or Admin access required."""
    return await crud_chat.get_unassigned_conversations(db=db)

@router.get("/team/{team_id}", response_model=List[ConversationResponse], dependencies=[Depends(require_permission("read", "conversations"))])
async def get_team_conversations(
    request: Request,
    team_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """Get conversations for a specific team. Admins can see any, agents only their team."""
    return await crud_chat.get_conversations_by_team(db=db, team_id=team_id)

@router.get("/customer/{customer_id}", response_model=List[ConversationResponse], dependencies=[Depends(require_permission("read", "customers"))])
async def get_conversations_by_customer(
    request: Request,
    customer_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Get all conversations for a specific customer.
    """
    # The customer was already fetched and validated by the dependency
    customer = request.state.resource
    return await crud_chat.get_conversations_by_customer(db=db, customer_id=customer.id)

@router.post("/{conversation_id}/labels", response_model=ConversationResponse, dependencies=[Depends(require_permission("update", "conversations"))])
async def attach_labels_to_conversation(
    request_obj: Request,
    background_tasks: BackgroundTasks,
    conversation_id: UUID,
    request: ConversationLabelRequest,
    db: AsyncSession = Depends(get_async_db),
):
    """Attach one or more labels to a conversation."""
    conversation = await crud_chat.get_conversation(db, id=conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    updated_conversation = await crud_label.attach_labels_to_conversation(
        db, conversation=conversation, label_ids=request.label_ids
    )

    if not updated_conversation:
        raise HTTPException(status_code=400, detail="One or more labels are invalid for this organization.")

    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request_obj,
        current_user=request_obj.state.user,
        action="conversation.attach_labels",
        target_resource=updated_conversation,
        details={"label_ids": request.label_ids}
    )
    return updated_conversation

@router.post("/{conversation_id}/suggest-replies", response_model=SuggestionResponse, dependencies=[Depends(require_permission("read", "conversations"))])
async def suggest_replies(
    request: Request,
    conversation_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Get AI-generated reply suggestions for a conversation.
    """
    suggestions = await ai_service.get_suggestions(conversation_id, db)
    return SuggestionResponse(suggestions=suggestions)


@router.get("/{conversation_id}/summary", response_model=SummaryResponse, dependencies=[Depends(require_permission("read", "conversations"))])
async def get_conversation_summary(
    request: Request,
    conversation_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Get an AI-generated summary of the entire conversation.
    """
    summary = await ai_service.get_summary(conversation_id, db)
    return SummaryResponse(summary=summary)


# --- NEW ARCHIVE ENDPOINTS ---

@router.post(
    "/{conversation_id}/archive",
    response_model=ConversationResponse,
    dependencies=[Depends(require_permission("update", "conversations"))]
)
async def archive_conversation_endpoint(
    request: Request,
    conversation_id: UUID,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Archives a conversation, hiding it from default lists.
    """
    current_user: User = request.state.user
    conversation = await crud_chat.archive_conversation(db, conversation_id=conversation_id)

    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found or could not be archived.")

    # Log the archival action
    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="conversation.archive",
        target_resource=conversation,
        details={"archived_by": current_user.email}
    )

    return conversation

@router.post(
    "/{conversation_id}/unarchive",
    response_model=ConversationResponse,
    dependencies=[Depends(require_permission("update", "conversations"))]
)
async def unarchive_conversation_endpoint(
    request: Request,
    conversation_id: UUID,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Un-archives a conversation, typically moving it to the 'closed' state.
    """
    current_user: User = request.state.user
    conversation = await crud_chat.unarchive_conversation(db, conversation_id=conversation_id)

    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found or was not archived.")

    # Log the un-archival action
    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action="conversation.unarchive",
        target_resource=conversation,
        details={"unarchived_by": current_user.email}
    )

    return conversation

@router.post(
    "/{conversation_id}/toggle-bot",
    response_model=ConversationResponse,
    dependencies=[Depends(require_permission("update", "conversations"))]
)
async def toggle_conversation_bot(
    request: Request,
    conversation_id: UUID,
    toggle_data: ConversationBotToggle,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db),
):
    """
    Enable or disable the automated bot for a specific conversation.
    """
    current_user: User = request.state.user

    # Use the new CRUD function to update the conversation
    conversation = await crud_chat.toggle_bot_activity(
        db,
        conversation_id=conversation_id,
        active_state=toggle_data.active
    )

    if not conversation:
        raise HTTPException(
            status_code=404,
            detail="Conversation not found"
        )

    # Log this important action
    action_str = "conversation.enable_bot" if toggle_data.active else "conversation.disable_bot"
    audit_log_service.log_activity(
        background_tasks=background_tasks,
        db=db,
        request=request,
        current_user=current_user,
        action=action_str,
        target_resource=conversation,
        details={"toggled_by": current_user.email}
    )

    return conversation

@router.post("/{conversation_id}/read", status_code=204, dependencies=[Depends(require_permission("update", "conversations"))])
async def mark_as_read(
    request: Request,
    conversation_id: UUID,
    db: AsyncSession = Depends(get_async_db),
):
    """Mark all messages in a conversation as read."""
    current_user: User = request.state.user
    await crud_chat.mark_messages_as_read(db, conversation_id=conversation_id, user_id=current_user.id)
    # Broadcast the event to the conversation channel
    await broadcast_message(
        {
            "type": "messages_read",
            "conversation_id": str(conversation_id),
            "read_by_user_id": str(current_user.id),
            "read_at": datetime.now(timezone.utc).isoformat(),
        },
        channel=f"conversation_{conversation_id}",
    )