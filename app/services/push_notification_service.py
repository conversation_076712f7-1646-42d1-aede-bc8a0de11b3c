import json
import logging
from typing import Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUI<PERSON>
from pywebpush import webpush, WebPushException

from app.core.config import settings
from app.crud.crud_push_subscription import crud_push_subscription
from app.schemas.push_subscription import PushNotificationPayload

logger = logging.getLogger(__name__)

class PushNotificationService:
    def __init__(self):
        self.vapid_private_key = settings.VAPID_PRIVATE_KEY
        self.vapid_claims = {"sub": f"mailto:{settings.VAPID_ADMIN_EMAIL}"}

    async def send_push_notification_to_user(
        self,
        db: AsyncSession,
        *,
        user_id: UUID,
        payload: Dict[str, Any]
    ) -> int:
        """
        Send a push notification to all devices of a specific user.
        Returns the number of successful notifications sent.
        """
        subscriptions = await crud_push_subscription.get_by_user_id(db, user_id=user_id)
        if not subscriptions:
            logger.info(f"No push subscriptions found for user {user_id}. Skipping.")
            return 0

        json_payload = json.dumps(payload)
        successful_sends = 0
        
        for sub in subscriptions:
            try:
                webpush(
                    subscription_info=sub.subscription_info,
                    data=json_payload,
                    vapid_private_key=self.vapid_private_key,
                    vapid_claims=self.vapid_claims
                )
                logger.info(f"Successfully sent push notification to user {user_id}")
                successful_sends += 1
            except WebPushException as ex:
                logger.error(f"Web push failed for user {user_id}: {ex}")
                # If the subscription is invalid (e.g., 404 Not Found or 410 Gone), delete it.
                if ex.response and ex.response.status_code in [404, 410]:
                    logger.warning(f"Subscription for user {user_id} is invalid. Deleting.")
                    endpoint = sub.subscription_info.get("endpoint", "")
                    await crud_push_subscription.remove_by_endpoint(
                        db, endpoint=endpoint, user_id=user_id
                    )
            except Exception as ex:
                logger.error(f"Unexpected error sending push notification to user {user_id}: {ex}")

        return successful_sends

    async def send_push_notification_to_users(
        self,
        db: AsyncSession,
        *,
        user_ids: List[UUID],
        payload: Dict[str, Any]
    ) -> int:
        """
        Send a push notification to multiple users.
        Returns the total number of successful notifications sent.
        """
        total_sent = 0
        for user_id in user_ids:
            sent = await self.send_push_notification_to_user(db, user_id=user_id, payload=payload)
            total_sent += sent
        return total_sent

    async def send_push_notification_to_organization(
        self,
        db: AsyncSession,
        *,
        organization_id: UUID,
        payload: Dict[str, Any]
    ) -> int:
        """
        Send a push notification to all users in an organization.
        Returns the number of successful notifications sent.
        """
        subscriptions = await crud_push_subscription.get_all_for_organization(
            db, organization_id=organization_id
        )
        if not subscriptions:
            logger.info(f"No push subscriptions found for organization {organization_id}. Skipping.")
            return 0

        json_payload = json.dumps(payload)
        successful_sends = 0
        
        for sub in subscriptions:
            try:
                webpush(
                    subscription_info=sub.subscription_info,
                    data=json_payload,
                    vapid_private_key=self.vapid_private_key,
                    vapid_claims=self.vapid_claims
                )
                logger.info(f"Successfully sent push notification to organization {organization_id}")
                successful_sends += 1
            except WebPushException as ex:
                logger.error(f"Web push failed for organization {organization_id}: {ex}")
                # If the subscription is invalid, delete it
                if ex.response and ex.response.status_code in [404, 410]:
                    logger.warning(f"Subscription is invalid. Deleting.")
                    endpoint = sub.subscription_info.get("endpoint", "")
                    await crud_push_subscription.remove_by_endpoint(
                        db, endpoint=endpoint, user_id=sub.user_id
                    )
            except Exception as ex:
                logger.error(f"Unexpected error sending push notification: {ex}")

        return successful_sends

    def create_notification_payload(
        self,
        title: str,
        body: str,
        *,
        icon: str = "/icon.png",
        badge: str = "/badge.png",
        data: Dict[str, Any] = None,
        actions: List[Dict[str, str]] = None,
        tag: str = "",
        require_interaction: bool = False
    ) -> Dict[str, Any]:
        """
        Create a standardized notification payload.
        """
        payload = {
            "title": title,
            "body": body,
            "icon": icon,
            "badge": badge,
            "data": data or {},
            "actions": actions or [],
            "tag": tag,
            "requireInteraction": require_interaction
        }
        return payload

# Global instance
push_notification_service = PushNotificationService()
