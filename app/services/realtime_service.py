
import json
from typing import Optional
from uuid import UUID
from app.core.websocket_manager import manager

def uuid_serializer(obj):
    """JSON serializer for UUID objects"""
    if isinstance(obj, UUID):
        return str(obj)
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

# This function now correctly broadcasts to the organization channel
async def send_new_conversation_notification(
    conversation_id: UUID,
    customer_name: str,
    organization_id: UUID
):
    """Broadcasts new conversation notifications to the entire organization."""
    notification = {
        "type": "new_conversation",
        "payload": {
            "conversation_id": conversation_id,
            "customer_name": customer_name,
            "message": f"New conversation started by {customer_name}"
        }
    }
    await manager.broadcast_to_organization(
        json.dumps(notification, default=uuid_serializer),
        organization_id
    )

# This function now correctly broadcasts to the specific chat channel
async def send_new_message_notification(
    conversation_id: UUID,
    message_data: dict # pass the full message dict
):
    """Broadcasts a new message within its conversation channel."""
    # The broadcast method expects a string, so we dump the dict to a JSON string here.
    message_string = json.dumps(message_data, default=uuid_serializer)
    await manager.broadcast_to_conversation(
        message_string,
        conversation_id
    )
