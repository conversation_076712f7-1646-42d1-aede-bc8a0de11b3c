from fastapi import BackgroundTasks, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Any

from app.crud.crud_activity_log import crud_activity_log
from app.schemas.activity_log import ActivityLogCreate
from app.models.user import User

class AuditLogService:
    async def _create_log_entry(self, db: AsyncSession, log_data: ActivityLogCreate):
        """Internal method to create the log entry in the database."""
        await crud_activity_log.create(db, obj_in=log_data)

    def log_activity(
        self,
        background_tasks: BackgroundTasks,
        db: AsyncSession,
        request: Request,
        current_user: User,
        action: str,
        target_resource: Any = None,
        details: dict = None
    ):
        """
        Creates an audit log entry in the background.
        
        Args:
            background_tasks: FastAPI's BackgroundTasks dependency.
            db: The database session.
            request: The request object to get the IP address.
            current_user: The user performing the action.
            action: A string describing the action (e.g., "user.create").
            target_resource: The SQLAlchemy model instance that was affected.
            details: A dict with any extra information to log.
        """

        ip_address = request.client.host if request.client else "unknown"
        
        user_agent = request.headers.get("user-agent", "unknown")

        log_data = ActivityLogCreate(
            user_id=current_user.id,
            user_email=current_user.email,
            action=action,
            ip_address=ip_address,    
            user_agent=user_agent,    
            details=details
        )
        
        if target_resource:
            # Safely get the tablename and id to handle potential None cases
            log_data.target_resource_type = getattr(target_resource, '__tablename__', None)
            log_data.target_resource_id = getattr(target_resource, 'id', None)

        background_tasks.add_task(self._create_log_entry, db, log_data)

audit_log_service = AuditLogService()
