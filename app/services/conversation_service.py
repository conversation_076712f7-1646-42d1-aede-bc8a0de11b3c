# yupcha-customerbot-ai/app/services/conversation_service.py

import logging
from fastapi import Depends, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_async_db
from app.crud import crud_chat
from app.schemas.chat import ConversationCreate
from app.models.chat import Conversation
from app.services.realtime_service import send_new_conversation_notification
from app.services.email_service import email_service
from app.services.push_notification_service import push_notification_service

logger = logging.getLogger(__name__)

class ConversationService:
    def __init__(self, db: AsyncSession = Depends(get_async_db)):
        self.db = db

    async def create_conversation(
        self, 
        conversation_data: ConversationCreate,
        background_tasks: BackgroundTasks
    ) -> Conversation:
        """
        Creates a new conversation, auto-assigns it, and triggers all necessary notifications.
        """
        try:
            new_conversation = await crud_chat.create_conversation(db=self.db, conversation=conversation_data)
            
            # relationships needed for notifications
            await self.db.refresh(new_conversation, ['assigned_team', 'customer', 'organization'])

            # real-time WebSocket notification
            await send_new_conversation_notification(
                conversation_id=new_conversation.id,
                customer_name=new_conversation.customer.name or f"Customer #{new_conversation.customer.id}",
                organization_id=new_conversation.organization_id
            )

            # background email and push notifications if a team was assigned
            if new_conversation.assigned_team_id:
                team_email = "<EMAIL>"

                # Email notification
                background_tasks.add_task(
                    email_service.send_new_conversation_notification,
                    team_email=team_email,
                    conversation_id=new_conversation.id,
                    customer_name=new_conversation.customer.name or f"Customer #{new_conversation.customer.id}"
                )

                # Push notification to team members
                customer_name = new_conversation.customer.name or f"Customer #{new_conversation.customer.id}"
                notification_payload = push_notification_service.create_notification_payload(
                    title=f"New Conversation from {customer_name}",
                    body="A new conversation has been assigned to your team.",
                    icon="/icon.png",
                    data={
                        "type": "new_conversation",
                        "conversation_id": str(new_conversation.id),
                        "url": f"/conversations/{new_conversation.id}"
                    },
                    tag=f"conversation_{new_conversation.id}",
                    require_interaction=True
                )

                # Send push notifications to all team members
                if new_conversation.assigned_team:
                    await self.db.refresh(new_conversation.assigned_team, ['users'])
                    for user in new_conversation.assigned_team.users:
                        if user.is_active:
                            background_tasks.add_task(
                                push_notification_service.send_push_notification_to_user,
                                self.db,
                                user_id=user.id,
                                payload=notification_payload
                            )

            logger.info(f"✅ Successfully created conversation {new_conversation.id}")
            return new_conversation

        except Exception as e:
            logger.error(f"❌ Error creating conversation: {e}")
            raise