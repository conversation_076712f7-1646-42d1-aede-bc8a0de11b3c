from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Optional
from uuid import UUID
from app.models.label import Label
from app.models.chat import Conversation
from app.schemas.label import LabelCreate, LabelUpdate

async def create_label(db: AsyncSession, *, obj_in: LabelCreate, organization_id: UUID) -> Label:
    db_obj = Label(**obj_in.model_dump(), organization_id=organization_id)
    db.add(db_obj)
    await db.commit()
    await db.refresh(db_obj)
    return db_obj

async def get_label(db: AsyncSession, *, id: UUID) -> Optional[Label]:
    """Get a single label by ID."""
    result = await db.execute(select(Label).filter(Label.id == id))
    return result.scalars().first()

async def get_labels_by_organization(db: AsyncSession, *, organization_id: UUID) -> List[Label]:
    result = await db.execute(
        select(Label)
        .filter(Label.organization_id == organization_id)
        .order_by(Label.name)
    )
    return result.scalars().all()

async def attach_labels_to_conversation(db: AsyncSession, *, conversation: Conversation, label_ids: List[UUID]) -> Conversation:
    """Attaches a list of labels to a conversation, replacing existing ones."""
    result = await db.execute(select(Label).filter(Label.id.in_(label_ids)))
    labels_to_attach = result.scalars().all()
    
    # Security check: ensure all labels belong to the same organization as the conversation
    for label in labels_to_attach:
        if label.organization_id != conversation.organization_id:
            return None # Or raise an exception

    conversation.labels = labels_to_attach
    db.add(conversation)
    await db.commit()
    await db.refresh(conversation)
    return conversation