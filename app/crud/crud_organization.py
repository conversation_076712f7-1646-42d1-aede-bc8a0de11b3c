from  datetime import datetime, timedelta, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from typing import List, Optional
from uuid import UUID

from app.models.organization import Organization
from app.schemas.organization import OrganizationCreate, OrganizationUpdate

async def create_organization(db: AsyncSession, organization: OrganizationCreate) -> Organization:
    """Create a new organization"""
    db_organization = Organization(**organization.model_dump())
    db.add(db_organization)
    await db.commit()
    await db.refresh(db_organization)
    return db_organization

from sqlalchemy.orm import selectinload, aliased
from sqlalchemy import func

# ... existing code ...

async def get_organization(db: AsyncSession, organization_id: UUID, include_deleted: bool = False, load_relations: bool = False) -> Optional[Organization]:
    """Get an organization by ID, optionally including soft-deleted ones and loading relations."""
    stmt = select(Organization).where(Organization.id == organization_id)
    if not include_deleted:
        stmt = stmt.where(Organization.is_deleted == False)
    
    if load_relations:
        # Load teams and users directly
        stmt = stmt.options(selectinload(Organization.teams), selectinload(Organization.users))

    result = await db.execute(stmt)
    return result.scalar_one_or_none()

async def get_organizations(db: AsyncSession, skip: int = 0, limit: int = 100, load_relations: bool = False) -> List[Organization]:
    """Get all non-deleted organizations with pagination, optionally loading relations and counts."""
    stmt = select(Organization).where(Organization.is_deleted == False)

    if load_relations:
        stmt = stmt.options(selectinload(Organization.teams), selectinload(Organization.users))

    stmt = stmt.offset(skip).limit(limit)
    result = await db.execute(stmt)
    return result.scalars().all()

async def get_organization_by_name(db: AsyncSession, name: str) -> Optional[Organization]:
    """Get an organization by name"""
    result = await db.execute(select(Organization).filter(Organization.name == name))
    return result.scalar_one_or_none()

async def update_organization(db: AsyncSession, organization_id: UUID, organization_update: OrganizationUpdate) -> Optional[Organization]:
    """Update an organization"""
    organization = await get_organization(db, organization_id)
    if organization:
        update_data = organization_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(organization, field, value)
        await db.commit()
        await db.refresh(organization)
    return organization

async def delete_organization(db: AsyncSession, organization_id: UUID) -> bool:
    """Delete an organization"""
    organization = await get_organization(db, organization_id)
    if organization:
        await db.delete(organization)
        await db.commit()
        return True
    return False

async def soft_delete_organization(db: AsyncSession, organization_id: UUID) -> Optional[Organization]:
    """Soft delete an organization by setting is_deleted to True."""
    organization = await get_organization(db, organization_id=organization_id)
    if organization:
        organization.is_deleted = True
        organization.deleted_at = datetime.utcnow()
        await db.commit()
        await db.refresh(organization)
    return organization

async def restore_organization(db: AsyncSession, organization_id: UUID) -> Optional[Organization]:
    """Restore a soft-deleted organization."""
    organization = await get_organization(db, organization_id=organization_id, include_deleted=True)
    if organization and organization.is_deleted:
        organization.is_deleted = False
        organization.deleted_at = None
        await db.commit()
        await db.refresh(organization)
    return organization

async def purge_deleted_organizations(db: AsyncSession) -> int:
    """Permanently delete organizations soft-deleted more than 90 days ago."""
    purge_date = datetime.now(timezone.utc) - timedelta(days=90)
    stmt = delete(Organization).where(Organization.is_deleted == True, Organization.deleted_at <= purge_date)
    result = await db.execute(stmt)
    await db.commit()
    return result.rowcount