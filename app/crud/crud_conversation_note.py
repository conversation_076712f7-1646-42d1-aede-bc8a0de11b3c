from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload # Import selectinload
from typing import List, Optional
from uuid import UUID
from app.models.conversation_note import ConversationNote
from app.schemas.conversation_note import ConversationNoteCreate, ConversationNoteUpdate

async def create_conversation_note(
    db: AsyncSession, *, note_in: ConversationNoteCreate
) -> ConversationNote:
    note = ConversationNote(**note_in.dict())
    db.add(note)
    await db.commit()
    await db.refresh(note)
    return note

async def get_conversation_notes_by_conversation(
    db: AsyncSession, *, conversation_id: UUID, skip: int = 0, limit: int = 100
) -> List[ConversationNote]:
    result = await db.execute(
        select(ConversationNote)
        .options(selectinload(ConversationNote.user)) # Eager load the user
        .filter(ConversationNote.conversation_id == conversation_id)
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()

async def get_conversation_note(
    db: AsyncSession, *, note_id: UUID
) -> Optional[ConversationNote]:
    result = await db.execute(select(ConversationNote)
        .options(selectinload(ConversationNote.user)) # Eager load the user
        .filter(ConversationNote.id == note_id))
    return result.scalars().first()

async def update_conversation_note(
    db: AsyncSession, *, note: ConversationNote, note_in: ConversationNoteUpdate
) -> ConversationNote:
    note_data = note_in.dict(exclude_unset=True)
    for field, value in note_data.items():
        setattr(note, field, value)
    await db.commit()
    await db.refresh(note)
    return note

async def delete_conversation_note(db: AsyncSession, *, note: ConversationNote):
    await db.delete(note)
    await db.commit()
