from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List, Optional
from sqlalchemy import Select
from uuid import UUID
from app.models.note import Note
from app.schemas.note import NoteCreate, NoteUpdate

def get_notes_query(customer_id: UUID) -> Select:
    """
    Returns a SQLAlchemy SELECT statement for fetching notes for a specific customer.
    """
    return select(Note).where(Note.customer_id == customer_id).order_by(Note.created_at.desc())

async def create_note(db: AsyncSession, *, note_in: NoteCreate) -> Note:
    note = Note(**note_in.dict())
    db.add(note)
    await db.commit()
    await db.refresh(note)
    return note

async def get_notes_by_customer(
    db: AsyncSession, *, customer_id: UUID, skip: int = 0, limit: int = 100
) -> List[Note]:
    result = await db.execute(
        select(Note)
        .filter(Note.customer_id == customer_id)
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()

async def get_note(db: AsyncSession, *, note_id: UUID) -> Optional[Note]:
    result = await db.execute(select(Note).filter(Note.id == note_id))
    return result.scalars().first()

async def update_note(
    db: AsyncSession, *, note: Note, note_in: NoteUpdate
) -> Note:
    note_data = note_in.dict(exclude_unset=True)
    for field, value in note_data.items():
        setattr(note, field, value)
    await db.commit()
    await db.refresh(note)
    return note

async def delete_note(db: AsyncSession, *, note: Note):
    await db.delete(note)
    await db.commit()
