from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from typing import List, Optional
from uuid import UUID

from app.models.push_subscription import PushSubscription
from app.schemas.push_subscription import PushSubscriptionCreate

class CRUDPushSubscription:
    async def get_by_user_id(self, db: AsyncSession, *, user_id: UUID) -> List[PushSubscription]:
        """Get all push subscriptions for a user"""
        result = await db.execute(select(PushSubscription).where(PushSubscription.user_id == user_id))
        return result.scalars().all()

    async def get_by_endpoint(self, db: AsyncSession, *, endpoint: str, user_id: UUID) -> Optional[PushSubscription]:
        """Get a push subscription by endpoint and user"""
        stmt = select(PushSubscription).where(
            PushSubscription.user_id == user_id,
            PushSubscription.subscription_info["endpoint"].as_string() == endpoint
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    async def create_for_user(self, db: AsyncSession, *, obj_in: PushSubscriptionCreate, user_id: UUID) -> PushSubscription:
        """Create a new push subscription for a user"""
        # Check if this exact subscription endpoint already exists for this user to avoid duplicates
        endpoint = obj_in.subscription_info.get("endpoint")
        existing = await self.get_by_endpoint(db, endpoint=endpoint, user_id=user_id)
        
        if existing:
            # Update the existing subscription with new info
            existing.subscription_info = obj_in.subscription_info
            await db.commit()
            await db.refresh(existing)
            return existing

        # Create new subscription
        db_obj = PushSubscription(user_id=user_id, subscription_info=obj_in.subscription_info)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def remove_by_endpoint(self, db: AsyncSession, *, endpoint: str, user_id: UUID) -> int:
        """Remove a push subscription by endpoint"""
        stmt = delete(PushSubscription).where(
            PushSubscription.user_id == user_id,
            PushSubscription.subscription_info["endpoint"].as_string() == endpoint
        )
        result = await db.execute(stmt)
        await db.commit()
        return result.rowcount

    async def remove_by_id(self, db: AsyncSession, *, subscription_id: UUID, user_id: UUID) -> int:
        """Remove a push subscription by ID"""
        stmt = delete(PushSubscription).where(
            PushSubscription.id == subscription_id,
            PushSubscription.user_id == user_id
        )
        result = await db.execute(stmt)
        await db.commit()
        return result.rowcount

    async def get_all_for_organization(self, db: AsyncSession, *, organization_id: UUID) -> List[PushSubscription]:
        """Get all push subscriptions for users in an organization"""
        stmt = select(PushSubscription).join(
            PushSubscription.user
        ).where(
            PushSubscription.user.has(organization_id=organization_id)
        )
        result = await db.execute(stmt)
        return result.scalars().all()

crud_push_subscription = CRUDPushSubscription()
