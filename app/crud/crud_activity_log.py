from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, Select
from app.crud.base import CRUDBase
from app.models.activity_log import ActivityLog
from app.schemas.activity_log import ActivityLogCreate
from typing import Optional
from uuid import UUID

def get_activity_logs_query(user_id: Optional[UUID] = None) -> Select:
    """
    Returns a SQLAlchemy SELECT statement for fetching activity logs with optional user filtering.
    """
    stmt = select(ActivityLog)
    if user_id:
        stmt = stmt.where(ActivityLog.user_id == user_id)
    return stmt.order_by(ActivityLog.created_at.desc())

class CRUDActivityLog(CRUDBase[ActivityLog, ActivityLogCreate, None]):
    async def get_by_user(self, db: AsyncSession, *, user_id: UUID, skip: int = 0, limit: int = 100):
        result = await db.execute(
            select(self.model)
            .where(self.model.user_id == user_id)
            .order_by(self.model.created_at.desc())
            .offset(skip).limit(limit)
        )
        return result.scalars().all()

crud_activity_log = CRUDActivityLog(ActivityLog)
