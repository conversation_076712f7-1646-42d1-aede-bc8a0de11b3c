from datetime import timedelta, timezone
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_, Select
from typing import List, Optional
from uuid import UUID

from app.models.team import Team
from app.schemas.team import TeamCreate, TeamUpdate

def get_teams_query(organization_id: UUID, search: Optional[str] = None) -> Select:
    """
    Returns a SQLAlchemy SELECT statement for fetching teams with optional search.
    """
    stmt = select(Team).where(Team.organization_id == organization_id, Team.is_deleted == False)
    if search:
        stmt = stmt.where(Team.name.ilike(f"%{search}%"))
    return stmt.order_by(Team.name)

async def create_team(db: AsyncSession, team: TeamCreate) -> Team:
    """Create a new team"""
    db_team = Team(**team.model_dump())
    db.add(db_team)
    await db.commit()
    await db.refresh(db_team)
    return db_team

from sqlalchemy.orm import selectinload, aliased
from sqlalchemy import func


async def get_team(db: AsyncSession, id: UUID, include_deleted: bool = False, load_relations: bool = False) -> Optional[Team]:
    """Get a team by ID, optionally including soft-deleted ones and loading relations."""
    stmt = select(Team).where(Team.id == id)
    if not include_deleted:
        stmt = stmt.where(Team.is_deleted == False)
    
    if load_relations:
        stmt = stmt.options(selectinload(Team.users))

    result = await db.execute(stmt)
    return result.scalar_one_or_none()

async def get_teams(db: AsyncSession, skip: int = 0, limit: int = 100, load_relations: bool = False) -> List[Team]:
    """Get all non-deleted teams with pagination, optionally loading relations and counts."""
    stmt = select(Team).where(Team.is_deleted == False)

    if load_relations:
        stmt = stmt.options(selectinload(Team.users))

    stmt = stmt.offset(skip).limit(limit)
    result = await db.execute(stmt)
    return result.scalars().all()

async def get_teams_by_organization(db: AsyncSession, organization_id: UUID, skip: int = 0, limit: int = 100, load_relations: bool = False) -> List[Team]:
    """Get teams by organization, optionally loading relations and counts."""
    stmt = select(Team).filter(Team.organization_id == organization_id)

    if load_relations:
        stmt = stmt.options(selectinload(Team.users))

    stmt = stmt.offset(skip).limit(limit)
    result = await db.execute(stmt)
    return result.scalars().all()

async def get_team_by_name(db: AsyncSession, *, name: str, organization_id: UUID) -> Optional[Team]:
    """Get a team by its name within a specific organization."""
    stmt = select(Team).where(
        and_(
            Team.name == name,
            Team.organization_id == organization_id,
            Team.is_deleted == False
        )
    )
    result = await db.execute(stmt)
    return result.scalar_one_or_none()


async def update_team(db: AsyncSession, team_id: UUID, team_update: TeamUpdate) -> Optional[Team]:
    """Update a team"""
    team = await get_team(db, id=team_id)
    if team:
        update_data = team_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(team, field, value)
        await db.commit()
        await db.refresh(team)
    return team

async def delete_team(db: AsyncSession, team_id: UUID) -> bool:
    """Delete a team"""
    team = await get_team(db, id=team_id)
    if team:
        await db.delete(team)
        await db.commit()
        return True
    return False

async def soft_delete_team(db: AsyncSession, team_id: UUID) -> Optional[Team]:
    """Soft delete a team by setting is_deleted to True."""
    team = await get_team(db, id=team_id)
    if team:
        team.is_deleted = True
        team.deleted_at = datetime.now()
        await db.commit()
        await db.refresh(team)
    return team

async def restore_team(db: AsyncSession, team_id: UUID) -> Optional[Team]:
    """Restore a soft-deleted team."""
    team = await get_team(db, id=team_id, include_deleted=True)
    if team and team.is_deleted:
        team.is_deleted = False
        team.deleted_at = None
        await db.commit()
        await db.refresh(team)
    return team

async def purge_deleted_teams(db: AsyncSession) -> int:
    """Permanently delete teams soft-deleted more than 90 days ago."""
    purge_date = datetime.now(timezone.utc) - timedelta(days=90)
    stmt = delete(Team).where(Team.is_deleted == True, Team.deleted_at <= purge_date)
    result = await db.execute(stmt)
    await db.commit()
    return result.rowcount