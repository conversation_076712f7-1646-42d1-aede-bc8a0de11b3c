from pathlib import Path
from fastapi import Depends, HTTPException, status, Cookie, Request
from sqlalchemy.ext.asyncio import AsyncSession
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadTimeSignature
from typing import Optional, Callable, Dict, Any
import logging
from typing import Any
import casbin

from app.core.config import settings
from app.core.permissions import  get_enforcer
from app.db.session import get_async_db
from app.crud import crud_asset, crud_canned_response, crud_conversation_note, crud_customer, crud_label, crud_note, crud_user, crud_chat, crud_team
from app.crud.crud_role import crud_role
from app.models.chat import Conversation
from app.models.customer import Customer
from app.models.user import User
from uuid import UUID

SESSION_COOKIE_NAME = settings.SESSION_COOKIE_NAME
serializer = URLSafeTimedSerializer(settings.SECRET_KEY)
logger = logging.getLogger(__name__)

async def get_current_user(
    session_cookie: Optional[str] = <PERSON><PERSON>(None, alias=SESSION_COOKIE_NAME),
    db: AsyncSession = Depends(get_async_db),
) -> User:
    """Dependency to get the current, active user with their role and organization loaded."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
    )
    if not session_cookie:
        raise credentials_exception
    try:
        user_id_str = serializer.loads(session_cookie, max_age=86400)
        user_id = UUID(user_id_str)
    except (SignatureExpired, BadTimeSignature, ValueError):
        raise credentials_exception

    user = await crud_user.get_user(db, id=user_id)
    
    if not user or not user.is_active or user.is_deleted:
        raise credentials_exception
        
    return user

async def get_customer_for_user(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
) -> Customer:
    """
    Dependency to get a customer from the URL path and verify it belongs 
    to the current user's organization.
    """
    if "customer_id" not in request.path_params:
        raise HTTPException(status_code=500, detail="customer_id not found in path parameters for this dependency.")

    try:
        customer_id = UUID(request.path_params["customer_id"])
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid customer ID format.")

    if not current_user.organization_id:
        raise HTTPException(status_code=403, detail="User is not associated with an organization.")

    customer = await crud_customer.get_customer(db, customer_id=customer_id)
    
    if not customer or customer.organization_id != current_user.organization_id:
        raise HTTPException(status_code=404, detail="Customer not found.")
        
    return customer

async def get_conversation_for_user(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
) -> Conversation:
    """
    Dependency to get a conversation from the URL path and verify it belongs 
    to the current user's organization.
    """
    if "conversation_id" not in request.path_params:
        raise HTTPException(status_code=500, detail="conversation_id not found in path parameters for this dependency.")

    try:
        conversation_id = UUID(request.path_params["conversation_id"])
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid conversation ID format.")

    if not current_user.organization_id:
        raise HTTPException(status_code=403, detail="User is not associated with an organization.")

    conversation = await crud_chat.get_conversation(db, id=conversation_id)

    if not conversation or conversation.organization_id != current_user.organization_id:
        raise HTTPException(status_code=404, detail="Conversation not found.")
        
    return conversation

async def get_resource(request: Request, db: AsyncSession) -> Any:
    """A helper dependency to extract a resource object from the request path."""
    resource_map = {
        "conversations": (crud_chat.get_conversation, "conversation_id"),
        "messages": (crud_chat.get_message, "message_id"),
        "users": (crud_user.get_user, "user_id"),
        "teams": (crud_team.get_team, "team_id"),
        "roles": (crud_role.get_role, "role_id"),
        "labels": (crud_label.get_label, "label_id"),
        "canned_responses": (crud_canned_response, "response_id"),
        "notes": (crud_note.get_note, "note_id"),
        "conversation_notes": (crud_conversation_note.get_conversation_note, "note_id"),
        "assets": (crud_asset, "asset_id"),
    }
    
    for resource_name, (crud_instance, id_key) in resource_map.items():
        if id_key in request.path_params:

            from uuid import UUID
            resource_id = UUID(request.path_params[id_key])
            if resource_name == "canned_responses":
                resource = await crud_instance.get_canned_response(db, response_id=resource_id)
            elif resource_name == "assets":
                resource = await crud_instance.get_asset(db, asset_id=resource_id)
            else:
                resource = await crud_instance(db, id=resource_id) # Use generic 'id'
            if not resource:
                raise HTTPException(status_code=404, detail=f"{resource_name.capitalize()} not found")
            request.state.resource = resource
            return resource
    return None

def require_permission(action: str):
    """
    Powerful ABAC dependency. Ensures the current user has permission
    to perform an action. It checks for permissions hierarchically.
    """
    async def permission_checker(
        request: Request,
        db: AsyncSession = Depends(get_async_db),
        current_user: User = Depends(get_current_user),
        enforcer: casbin.Enforcer = Depends(get_enforcer)
    ):
        request.state.user = current_user
        resource = await get_resource(request, db)
        user_role = current_user.role_name  # Single role now

        if not user_role:
            raise HTTPException(status_code=403, detail="Access Denied: No role assigned.")

        if resource:
            obj_specific = f"company:{current_user.organization_id}:{resource.__tablename__[:-1]}:{resource.id}"
            if enforcer.enforce(user_role, obj_specific, action):
                return 

        resource_type = request.url.path.split('/')[3]
        obj_general = f"company:{current_user.organization_id}:{resource_type}"
        if enforcer.enforce(user_role, obj_general, action):
            return 
        
        raise HTTPException(status_code=403, detail="Permission Denied")

    return permission_checker

# Convenience dependencies for common roles
def require_admin():
    """Convenience dependency for Admin role"""
    return require_role("Admin")

def require_agent():
    """Convenience dependency for Agent role"""
    return require_role("Agent")

def require_role(required_role: str):
    """
    Dependency generator that checks if the current user has a specific role.
    SuperAdmin users automatically pass all role checks.

    Usage:
        @router.get("/admin-only", dependencies=[Depends(require_role("Admin"))])
        or
        @router.get("/admin-only")
        async def admin_endpoint(current_user: User = Depends(require_role("Admin"))):
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        # SuperAdmin has access to everything
        if current_user.role_name == "SuperAdmin":
            return current_user

        if not current_user.has_role(required_role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required role: '{required_role}' (user has: '{current_user.role_name}')",
            )
        return current_user
    return role_checker

def require_any_role(*required_roles: str):
    """
    Dependency generator that checks if the current user has ANY of the specified roles.
    SuperAdmin users automatically pass all role checks.

    Usage:
        @router.get("/admin-only")
        async def endpoint(current_user: User = Depends(require_any_role("Admin", "Manager"))):
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        user_role = current_user.role_name

        # SuperAdmin has access to everything
        if user_role == "SuperAdmin":
            return current_user

        if user_role not in required_roles:
            roles_str = "', '".join(required_roles)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: '{roles_str}' (user has: '{user_role}')",
            )
        return current_user
    return role_checker

def require_all_roles(*required_roles: str):
    """
    Dependency generator that checks if the current user has ALL of the specified roles.
    Note: Since users have only one role, this will only work if exactly one role is required.

    Usage:
        @router.get("/super-admin")
        async def endpoint(current_user: User = Depends(require_all_roles("Admin"))):
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        user_role = current_user.role_name

        # Since users have only one role, this only makes sense with one required role
        # or if the user is SuperAdmin (which has all permissions)
        if user_role == "SuperAdmin":
            return current_user

        if len(required_roles) == 1 and user_role == required_roles[0]:
            return current_user

        roles_str = "', '".join(required_roles)
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Access denied. Required roles: '{roles_str}' (user has: '{user_role}')",
        )
    return role_checker

def require_super_admin():
    """Convenience dependency for SuperAdmin role"""
    return require_role("SuperAdmin")
