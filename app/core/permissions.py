import casbin
import os
from casbin_sqlalchemy_adapter import Adapter
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

current_dir = os.path.dirname(os.path.abspath(__file__))
app_dir = os.path.join(current_dir, '..')
model_path = os.path.join(app_dir, 'casbin', 'casbin_model.conf')

adapter = Adapter(str(settings.DATABASE_URL))

enforcer = casbin.Enforcer(model_path, adapter)



def get_enforcer():
    """Returns the Casbin enforcer instance."""
    return enforcer