import boto3
from botocore.exceptions import ClientError
from botocore.config import Config
import os
from typing import Optional, BinaryIO
import uuid
from datetime import datetime
import mimetypes
from app.core.config import settings

class S3Manager:
    def __init__(self):
        # MinIO/S3 Configuration from settings
        self.endpoint_url = settings.S3.ENDPOINT_URL
        self.access_key = settings.S3.ACCESS_KEY
        self.secret_key = settings.S3.SECRET_KEY
        self.bucket_name = settings.S3.BUCKET_NAME
        self.region = settings.S3.REGION

        # Define file type constants
        self.ALLOWED_IMAGE_TYPES = {"image/jpeg", "image/png", "image/gif", "image/webp"}
        self.ALLOWED_VIDEO_TYPES = {"video/mp4", "video/webm", "video/mov", "video/avi"}
        self.ALLOWED_AUDIO_TYPES = {"audio/mp3", "audio/wav", "audio/ogg", "audio/m4a"}
        self.ALLOWED_DOCUMENT_TYPES = {"application/pdf", "text/plain", "application/msword"}

        # Define size limits
        self.MAX_FILE_SIZE = 30 * 1024 * 1024  # 50MB
        self.MAX_IMAGE_SIZE = 10 * 1024 * 1024  # 10MB
        self.MAX_VIDEO_SIZE = 100 * 1024 * 1024  # 100MB
        
        # Create S3 client
        self.s3_client = boto3.client(
            's3',
            endpoint_url=self.endpoint_url,
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key,
            region_name=self.region,
            config=Config(signature_version='s3v4')
        )
        
        # Ensure bucket exists
        self._ensure_bucket_exists()
    
    def _ensure_bucket_exists(self):
        """Create bucket if it doesn't exist"""
        try:
            self.s3_client.head_bucket(Bucket=self.bucket_name)
        except ClientError as e:
            error_code = int(e.response['Error']['Code'])
            if error_code == 404:
                # Bucket doesn't exist, create it
                try:
                    if self.region == 'us-east-1':
                        self.s3_client.create_bucket(Bucket=self.bucket_name)
                    else:
                        self.s3_client.create_bucket(
                            Bucket=self.bucket_name,
                            CreateBucketConfiguration={'LocationConstraint': self.region}
                        )
                    print(f"✅ Created S3 bucket: {self.bucket_name}")
                except ClientError as create_error:
                    print(f"❌ Failed to create bucket: {create_error}")
            else:
                print(f"❌ Error checking bucket: {e}")

    async def ensure_bucket_exists(self):
        """Public async method to ensure bucket exists"""
        import asyncio
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self._ensure_bucket_exists)
    
    def generate_file_key(self, original_filename: str, file_type: str) -> str:
        """Generate unique S3 key for file"""
        # Get file extension
        _, ext = os.path.splitext(original_filename)
        
        # Generate unique filename
        timestamp = datetime.now().strftime("%Y/%m/%d")
        unique_id = str(uuid.uuid4())
        
        # Create S3 key: media/type/year/month/day/uuid.ext
        s3_key = f"media/{file_type}/{timestamp}/{unique_id}{ext}"
        return s3_key
    
    def upload_file(self, file_obj: BinaryIO, filename: str, content_type: str) -> tuple[str, str]:
        """
        Upload file to S3/MinIO
        Returns: (s3_key, s3_url)
        """
        try:
            # Determine file type from content type
            file_type = self._get_file_type_from_content_type(content_type)
            
            # Generate S3 key
            s3_key = self.generate_file_key(filename, file_type)
            
            # Upload file
            self.s3_client.upload_fileobj(
                file_obj,
                self.bucket_name,
                s3_key,
                ExtraArgs={
                    'ContentType': content_type,
                    'ACL': 'public-read'  # Make files publicly accessible
                }
            )
            
            # Generate public URL
            s3_url = f"{self.endpoint_url}/{self.bucket_name}/{s3_key}"
            
            return s3_key, s3_url
            
        except ClientError as e:
            print(f"❌ Failed to upload file: {e}")
            raise
    
    def delete_file(self, s3_key: str) -> bool:
        """Delete file from S3/MinIO"""
        try:
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=s3_key)
            return True
        except ClientError as e:
            print(f"❌ Failed to delete file: {e}")
            return False
    
    def generate_presigned_url(self, s3_key: str, expiration: int = 3600) -> Optional[str]:
        """Generate presigned URL for private files"""
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': s3_key},
                ExpiresIn=expiration
            )
            return url
        except ClientError as e:
            print(f"❌ Failed to generate presigned URL: {e}")
            return None
    
    def _get_file_type_from_content_type(self, content_type: str) -> str:
        """Determine file type from MIME type"""
        if content_type.startswith('image/'):
            if 'gif' in content_type:
                return 'gif'
            return 'image'
        elif content_type.startswith('video/'):
            return 'video'
        elif content_type.startswith('audio/'):
            return 'audio'
        else:
            return 'document'
    
    def get_file_info(self, s3_key: str) -> Optional[dict]:
        """Get file metadata from S3"""
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
            return {
                'size': response['ContentLength'],
                'content_type': response['ContentType'],
                'last_modified': response['LastModified']
            }
        except ClientError:
            return None

# Global S3 manager instance
s3_manager = S3Manager()
