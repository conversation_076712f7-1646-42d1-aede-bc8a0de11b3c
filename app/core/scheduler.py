from apscheduler.schedulers.asyncio import AsyncIOScheduler
from app.db.session import AsyncSessionLocal
from app.crud.crud_user import purge_deleted_users
from app.crud.crud_team import purge_deleted_teams
from app.crud.crud_organization import purge_deleted_organizations
from app.crud.crud_customer import purge_deleted_customers

scheduler = AsyncIOScheduler()

async def purge_all_soft_deleted_data():
    """A single job that purges all soft-deleted data older than 90 days."""
    print("🧹 Running scheduled purge of soft-deleted data...")
    async with AsyncSessionLocal() as db:
        purged_users = await purge_deleted_users(db)
        print(f"   - Purged {purged_users} users.")
        
        #* create these purge functions in their respective CRUD files
        purged_teams = await purge_deleted_teams(db)
        print(f"   - Purged {purged_teams} teams.")
        
        purged_orgs = await purge_deleted_organizations(db)
        print(f"   - Purged {purged_orgs} organizations.")

        purged_customers = await purge_deleted_customers(db)
        print(f"   - Purged {purged_customers} customers.")
    print("✅ Purge complete.")

def setup_scheduler():
    """Adds jobs to the scheduler and starts it."""
    #* Schedule the purge job to run once every day
    scheduler.add_job(purge_all_soft_deleted_data, 'interval', days=1)
    scheduler.start()
    print("🕒 Scheduler started. Purge job will run daily.")