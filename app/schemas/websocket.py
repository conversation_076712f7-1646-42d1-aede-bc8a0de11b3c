from pydantic import BaseModel, Field
from typing import Literal, Optional, Union
from uuid import UUID

class WebSocketMessageIn(BaseModel):
    """Base model for all incoming WebSocket messages."""
    type: str

class PingMessageIn(WebSocketMessageIn):
    type: Literal["ping"] = "ping"

class TextMessageIn(WebSocketMessageIn):
    type: Literal["text"] = "text"
    content: str = Field(..., min_length=1, max_length=4096)

class MediaMessageIn(WebSocketMessageIn):
    type: Literal["media"] = "media"
    asset_id: UUID
    content: Optional[str] = Field(None, max_length=1024) 

class TypingIndicatorIn(WebSocketMessageIn):
    type: Literal["typing"]
    is_typing: bool

# Union type to easily parse any of the allowed incoming message types
IncomingMessage = Union[PingMessageIn, TextMessageIn, MediaMessageIn, TypingIndicatorIn]