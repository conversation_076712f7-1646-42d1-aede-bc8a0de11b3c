from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from uuid import UUID

# Shared properties
class TeamBase(BaseModel):
    name: str
    description: Optional[str] = None
    organization_id: UUID
    is_active: bool = True

# Properties to receive via API on creation
class TeamCreate(TeamBase):
    pass

# Properties to receive via API on update
class TeamUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    organization_id: Optional[UUID] = None
    is_active: Optional[bool] = None

# Properties to return to client
class TeamResponse(TeamBase):
    id: UUID
    users_count: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# For backward compatibility
Team = TeamResponse
