from pydantic import BaseModel, EmailStr, computed_field
from typing import Optional, List
from datetime import datetime
from uuid import UUID

from app.schemas.role import RoleResponse

# Shared properties
class UserBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None
    organization_id: Optional[UUID] = None
    team_id: Optional[UUID] = None
    is_active: bool = True

# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str
    role_id: Optional[UUID] = None  # Optional role assignment

# Properties to receive via API on update
class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    email: Optional[EmailStr] = None
    organization_id: Optional[UUID] = None
    team_id: Optional[UUID] = None
    is_active: Optional[bool] = None
    profile_image_id: Optional[UUID] = None
    role_id: Optional[UUID] = None  # Allow role updates

# Properties to return to client
class UserResponse(UserBase):
    id: UUID
    role: Optional[RoleResponse] = None  # Single role instead of list
    created_at: datetime
    updated_at: Optional[datetime] = None
    @computed_field
    @property
    def profile_image_url(self) -> Optional[str]:
        if hasattr(self, 'profile_image') and self.profile_image:
            return self.profile_image.s3_url
        return None

    class Config:
        from_attributes = True 

User = UserResponse
