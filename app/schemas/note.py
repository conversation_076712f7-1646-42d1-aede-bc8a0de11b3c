from pydantic import BaseModel
from datetime import datetime
from typing import Optional
from uuid import UUID

class NoteBase(BaseModel):
    content: str

class NoteCreate(NoteBase):
    customer_id: UUID
    user_id: UUID

class NoteUpdate(NoteBase):
    pass

class NoteInDBBase(NoteBase):
    id: UUID
    customer_id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Note(NoteInDBBase):
    pass
