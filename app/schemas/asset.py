from pydantic import BaseModel
from datetime import datetime
from uuid import UUID
from typing import Optional
from app.models.asset import AssetType

# Shared properties
class AssetBase(BaseModel):
    filename: str
    original_filename: str
    file_type: AssetType
    mime_type: str
    file_size: int
    s3_bucket: str
    s3_key: str
    s3_url: str
    width: Optional[int] = None
    height: Optional[int] = None
    duration: Optional[int] = None
    thumbnail_s3_key: Optional[str] = None
    thumbnail_s3_url: Optional[str] = None
    is_processed: bool = False
    file_metadata: Optional[str] = None

# Properties to receive via API on creation
class AssetCreate(AssetBase):
    pass

# Properties to receive via API on update
class AssetUpdate(BaseModel):
    filename: Optional[str] = None
    is_processed: Optional[bool] = None
    thumbnail_s3_key: Optional[str] = None
    thumbnail_s3_url: Optional[str] = None
    file_metadata: Optional[str] = None

# Properties to return via API
class AssetResponse(AssetBase):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# For file upload response
class FileUploadResponse(BaseModel):
    asset_id: UUID
    filename: str
    file_type: AssetType
    file_size: int
    s3_url: str
    thumbnail_url: Optional[str] = None
    
    class Config:
        from_attributes = True
