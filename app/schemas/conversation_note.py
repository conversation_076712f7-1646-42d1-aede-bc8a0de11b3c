from pydantic import BaseModel
from datetime import datetime
from typing import Optional
from uuid import UUID

class ConversationNoteBase(BaseModel):
    content: str

class ConversationNoteCreate(ConversationNoteBase):
    conversation_id: UUID
    user_id: UUID

class ConversationNoteUpdate(ConversationNoteBase):
    pass

from app.schemas.user import UserResponse # Import UserResponse

class ConversationNoteInDBBase(ConversationNoteBase):
    id: UUID
    conversation_id: UUID
    user_id: UUID
    user: Optional[UserResponse] = None # Include user details
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ConversationNote(ConversationNoteInDBBase):
    pass
