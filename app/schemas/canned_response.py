from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from uuid import UUID

class CannedResponseBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255, description="Response title")
    content: str = Field(..., min_length=1, description="Response content/text")
    shortcut: Optional[str] = Field(None, max_length=50, description="Quick access shortcut (e.g., /hello)")
    category: Optional[str] = Field(None, max_length=100, description="Response category")
    is_public: bool = Field(True, description="Whether response is visible to all users in organization")

class CannedResponseCreate(CannedResponseBase):
    organization_id: Optional[UUID] = Field(None, description="Organization ID (auto-filled from current user if not provided)")

class CannedResponseUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255, description="Response title")
    content: Optional[str] = Field(None, min_length=1, description="Response content/text")
    shortcut: Optional[str] = Field(None, max_length=50, description="Quick access shortcut")
    category: Optional[str] = Field(None, max_length=100, description="Response category")
    is_public: Optional[bool] = Field(None, description="Whether response is visible to all users")
    is_active: Optional[bool] = Field(None, description="Whether response is active")

class CannedResponseInDB(CannedResponseBase):
    id: UUID
    organization_id: UUID
    created_by: UUID
    is_active: bool
    usage_count: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_used_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class CannedResponseResponse(CannedResponseInDB):
    """Response model for canned response data"""
    pass

class CannedResponseWithCreator(CannedResponseResponse):
    """Canned response with creator information"""
    creator_name: str
    creator_email: str

class CannedResponseUsage(BaseModel):
    """Model for tracking canned response usage"""
    response_id: UUID
    used_by: UUID
    used_at: datetime
    conversation_id: Optional[UUID] = None
    message_id: Optional[UUID] = None

# Search and filtering schemas
class CannedResponseSearch(BaseModel):
    query: Optional[str] = Field(None, description="Search in title and content")
    category: Optional[str] = Field(None, description="Filter by category")
    created_by: Optional[UUID] = Field(None, description="Filter by creator")
    is_public: Optional[bool] = Field(None, description="Filter by public/private")
    shortcut: Optional[str] = Field(None, description="Search by shortcut")

class CannedResponseStats(BaseModel):
    total_responses: int
    public_responses: int
    private_responses: int
    active_responses: int
    inactive_responses: int
    total_usage: int
    most_used_response: Optional[CannedResponseResponse] = None
    categories: List[str]

# Bulk operations
class BulkCannedResponseCreate(BaseModel):
    responses: List[CannedResponseCreate] = Field(..., description="List of canned responses to create")

class BulkCannedResponseUpdate(BaseModel):
    response_ids: List[UUID] = Field(..., description="List of response IDs to update")
    updates: CannedResponseUpdate = Field(..., description="Updates to apply to all responses")

# Category management
class CannedResponseCategory(BaseModel):
    name: str
    count: int
    description: Optional[str] = None

class CannedResponsesByCategory(BaseModel):
    category: str
    responses: List[CannedResponseResponse]
