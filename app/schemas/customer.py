from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime
from uuid import UUID

# Shared properties
class CustomerBase(BaseModel):
    customer_id: str
    organization_id: UUID
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    ip_address: Optional[str] = None
    location: Optional[str] = None
    user_agent: Optional[str] = None
    customer_metadata: Optional[str] = None
    is_active: bool = True

# Properties to receive via API on creation
class CustomerCreate(CustomerBase):
    pass

# Properties to receive via API on update
class CustomerUpdate(BaseModel):
    customer_id: Optional[str] = None
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    ip_address: Optional[str] = None
    location: Optional[str] = None
    user_agent: Optional[str] = None
    customer_metadata: Optional[str] = None
    is_active: Optional[bool] = None

# Properties to return to client
class CustomerResponse(CustomerBase):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# For backward compatibility
Customer = CustomerResponse
