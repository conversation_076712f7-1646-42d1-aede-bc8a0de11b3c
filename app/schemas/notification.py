"""
Notification schemas for real-time notifications
"""

from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum
from uuid import UUID

class NotificationType(str, Enum):
    """Types of notifications"""
    NEW_MESSAGE = "new_message"
    NEW_CONVERSATION = "new_conversation"
    CONVERSATION_ASSIGNED = "conversation_assigned"
    CONVERSATION_CLOSED = "conversation_closed"
    AGENT_JOINED = "agent_joined"
    AGENT_LEFT = "agent_left"
    SYSTEM_ALERT = "system_alert"

class NotificationPriority(str, Enum):
    """Notification priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

class NotificationBase(BaseModel):
    """Base notification schema"""
    type: NotificationType
    title: str
    message: str
    priority: NotificationPriority = NotificationPriority.NORMAL
    data: Optional[Dict[str, Any]] = None
    organization_id: Optional[UUID] = None
    team_id: Optional[UUID] = None
    user_id: Optional[UUID] = None  # Specific user (optional)
    conversation_id: Optional[UUID] = None
    customer_id: Optional[UUID] = None

class NotificationCreate(NotificationBase):
    """Schema for creating notifications"""
    pass

class NotificationResponse(NotificationBase):
    """Schema for notification responses"""
    id: str
    timestamp: datetime
    read: bool = False
    
    class Config:
        from_attributes = True

class MessageNotificationData(BaseModel):
    """Specific data for message notifications"""
    message_id: UUID
    conversation_id: UUID
    customer_id: UUID
    customer_name: str
    message_content: str
    message_type: str
    organization_id: UUID
    team_id: Optional[UUID] = None

class ConversationNotificationData(BaseModel):
    """Specific data for conversation notifications"""
    conversation_id: UUID
    customer_id: UUID
    customer_name: str
    organization_id: UUID
    team_id: Optional[UUID] = None
    status: str

class AgentNotificationData(BaseModel):
    """Specific data for agent notifications"""
    agent_id: UUID
    agent_name: str
    organization_id: UUID
    team_id: Optional[UUID] = None
    conversation_id: Optional[UUID] = None

# Notification factory functions
def create_new_message_notification(
    message_id: UUID,
    conversation_id: UUID,
    customer_id: UUID,
    customer_name: str,
    message_content: str,
    organization_id: UUID,
    team_id: Optional[UUID] = None
) -> NotificationCreate:
    """Create a new message notification"""
    return NotificationCreate(
        type=NotificationType.NEW_MESSAGE,
        title=f"New message from {customer_name}",
        message=message_content[:100] + "..." if len(message_content) > 100 else message_content,
        priority=NotificationPriority.HIGH,
        organization_id=organization_id,
        team_id=team_id,
        conversation_id=conversation_id,
        customer_id=customer_id,
        data=MessageNotificationData(
            message_id=message_id,
            conversation_id=conversation_id,
            customer_id=customer_id,
            customer_name=customer_name,
            message_content=message_content,
            message_type="customer",
            organization_id=organization_id,
            team_id=team_id
        ).model_dump()
    )

def create_new_conversation_notification(
    conversation_id: UUID,
    customer_id: UUID,
    customer_name: str,
    organization_id: UUID,
    team_id: Optional[UUID] = None
) -> NotificationCreate:
    """Create a new conversation notification"""
    return NotificationCreate(
        type=NotificationType.NEW_CONVERSATION,
        title=f"New conversation started",
        message=f"{customer_name} started a new conversation",
        priority=NotificationPriority.NORMAL,
        organization_id=organization_id,
        team_id=team_id,
        conversation_id=conversation_id,
        customer_id=customer_id,
        data=ConversationNotificationData(
            conversation_id=conversation_id,
            customer_id=customer_id,
            customer_name=customer_name,
            organization_id=organization_id,
            team_id=team_id,
            status="active"
        ).model_dump()
    )

def create_agent_joined_notification(
    agent_id: UUID,
    agent_name: str,
    conversation_id: UUID,
    organization_id: UUID,
    team_id: Optional[UUID] = None
) -> NotificationCreate:
    """Create an agent joined notification"""
    return NotificationCreate(
        type=NotificationType.AGENT_JOINED,
        title=f"Agent joined conversation",
        message=f"{agent_name} joined the conversation",
        priority=NotificationPriority.LOW,
        organization_id=organization_id,
        team_id=team_id,
        conversation_id=conversation_id,
        data=AgentNotificationData(
            agent_id=agent_id,
            agent_name=agent_name,
            organization_id=organization_id,
            team_id=team_id,
            conversation_id=conversation_id
        ).model_dump()
    )
