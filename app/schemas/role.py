from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from uuid import UUID

class RoleBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=50, description="Role name")
    description: Optional[str] = Field(None, max_length=255, description="Role description")
    company_id: Optional[UUID] = Field(None, description="Company/Organization ID for this role")

class RoleCreate(RoleBase):
    is_system_role: Optional[bool] = Field(False, description="Whether this is a system role (cannot be deleted)")

class RoleUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=50, description="Role name")
    description: Optional[str] = Field(None, max_length=255, description="Role description")
    is_active: Optional[bool] = Field(None, description="Whether the role is active")

class RoleInDB(RoleBase):
    id: UUID
    is_active: bool
    is_system_role: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class RoleResponse(RoleInDB):
    """Response model for role data"""
    pass

class RoleWithUsers(RoleResponse):
    """Role response with user count"""
    user_count: int

# User-Role assignment schemas
class UserRoleAssignment(BaseModel):
    user_id: UUID
    role_ids: List[UUID] = Field(..., description="List of role IDs to assign to the user")

class UserRoleResponse(BaseModel):
    user_id: UUID
    roles: List[RoleResponse]
    
    class Config:
        from_attributes = True

# Role assignment for bulk operations
class BulkRoleAssignment(BaseModel):
    user_ids: List[UUID] = Field(..., description="List of user IDs")
    role_ids: List[UUID] = Field(..., description="List of role IDs to assign")
    replace_existing: bool = Field(False, description="Whether to replace existing roles or add to them")

# Role statistics
class RoleStats(BaseModel):
    total_roles: int
    active_roles: int
    system_roles: int
    custom_roles: int
    users_with_roles: int
    users_without_roles: int
