from pydantic import BaseModel
from typing import Dict, Any
from uuid import UUID
from datetime import datetime

class PushSubscriptionCreate(BaseModel):
    """Schema for creating a new push subscription"""
    subscription_info: Dict[str, Any]

class PushSubscriptionResponse(BaseModel):
    """Schema for push subscription response"""
    id: UUID
    user_id: UUID
    created_at: datetime

    class Config:
        from_attributes = True

class PushNotificationPayload(BaseModel):
    """Schema for push notification payload"""
    title: str
    body: str
    icon: str = "/icon.png"
    badge: str = "/badge.png"
    data: Dict[str, Any] = {}
    actions: list = []
    tag: str = ""
    requireInteraction: bool = False
