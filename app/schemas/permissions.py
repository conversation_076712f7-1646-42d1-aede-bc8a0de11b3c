from typing import Dict, Any, List
from pydantic import BaseModel


class PermissionCheckRequest(BaseModel):
    resource: str
    action: str

class PermissionCheckResponse(BaseModel):
    allowed: bool
    user_role: str
    resource: str
    action: str

class RolePermissionsResponse(BaseModel):
    role: str
    permissions: List[Dict[str, Any]]
    

class AddPermissionRequest(BaseModel):
    role: str
    resource: str
    action: str
    
class RemovePermissionRequest(BaseModel):
    role: str
    resource: str
    action: str