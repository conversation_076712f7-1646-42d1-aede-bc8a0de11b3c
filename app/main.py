from contextlib import asynccontextmanager
from app.core.scheduler import setup_scheduler, scheduler
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from scalar_fastapi import get_scalar_api_reference
from app.api.api import api_router
from app.core.config import settings
from app.core.s3 import s3_manager

from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from redis import asyncio as aioredis
import logging
from app.core.broadcaster import broadcaster

from app.core.permissions import enforcer
from fastapi_pagination import add_pagination

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    print("🚀 Starting Yupcha Customer Bot AI...")
    
    await broadcaster.connect()

    try:
        await s3_manager.ensure_bucket_exists()
        print("✅ S3/MinIO bucket initialized successfully")
    except Exception as e:
        print(f"⚠️  S3/MinIO bucket initialization failed: {e}")

    try:
        redis = aioredis.from_url(
            settings.REDIS_URL, 
            encoding="utf-8", 
            decode_responses=True,
            health_check_interval=30,
            socket_connect_timeout=5,
            socket_keepalive=True
        )
        FastAPICache.init(RedisBackend(redis), prefix="fastapi-cache")
        print("✅ Redis cache initialized successfully")
    except Exception as e:
        print(f"⚠️ Redis connection failed: {e}")
        print("   Caching will be disabled.")

    # Load Casbin policies from DB
    try:
        enforcer.load_policy()
        print("✅ Casbin policies loaded from database")
    except Exception as e:
        print(f"⚠️ Failed to load Casbin policies: {e}")
        
    
    setup_scheduler()
    
    yield

    await broadcaster.disconnect()

    

    # Shutdown
    scheduler.shutdown()
    await FastAPICache.clear()
    print("🛑 Shutting down Yupcha Customer Searvice app...")

app = FastAPI(
    title=settings.APP_NAME,
    description="A production-ready, enterprise-level customer support system with real-time chat, advanced file handling, intelligent conversation management, and robust access control. Features include multi-tenancy, dynamic role-based access control (RBAC) with Casbin, scalable WebSocket communication via Redis Pub/Sub, AI-powered reply suggestions and conversation summaries, comprehensive audit logging, and soft-deletion with scheduled data purging.",
    debug=settings.DEBUG,
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS or [
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:3000",
        "http://127.0.0.1:3000"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

add_pagination(app)

app.include_router(api_router, prefix="/api")

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Welcome to Yupcha Customer Bot AI",
        "docs": "/docs",
        "redoc": "/redoc",
        "openapi": "/openapi.json",
        "endpoints": {
            "users": "/api/v1/users/",
            "conversations": "/api/v1/conversations/",
            "messages": "/api/v1/messages/",
            "media": "/api/v1/media/",
            "websocket_chat": "/api/v1/ws/chat/{conversation_id}",
            "websocket_notifications": "/api/v1/ws/agent-notifications",
            "health": "/health",
            "scalar_docs": "/scalar"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "app_name": settings.APP_NAME,
        "database": "connected"
    }

@app.get("/api")
async def api_info():
    """API information endpoint"""
    return {
        "api_version": "v1",
        "app_name": settings.APP_NAME,
        "description": "FastAPI-based customer chatbot with WebSocket support",
        "endpoints": {
            "authentication": {
                "login": "POST /api/v1/auth/login",
                "logout": "POST /api/v1/auth/logout",
                "me": "GET /api/v1/auth/me"
            },
            "users": {
                "create": "POST /api/v1/users/",
                "list_all": "GET /api/v1/users/",
                "list_agents": "GET /api/v1/users/agents",
                "list_admins": "GET /api/v1/users/admins",
                "get": "GET /api/v1/users/{id}"
            },
            "organizations": {
                "list": "GET /api/v1/organizations/",
                "create": "POST /api/v1/organizations/",
                "get": "GET /api/v1/organizations/{id}"
            },
            "customers": {
                "list": "GET /api/v1/customers/",
                "create": "POST /api/v1/customers/",
                "get": "GET /api/v1/customers/{id}"
            },
            "teams": {
                "list": "GET /api/v1/teams/",
                "create": "POST /api/v1/teams/",
                "get": "GET /api/v1/teams/{id}"
            },
            "conversations": {
                "list": "GET /api/v1/conversations/",
                "create": "POST /api/v1/conversations/",
                "get": "GET /api/v1/conversations/{id}",
                "messages": "GET /api/v1/conversations/{id}/messages",
                "assign_team": "POST /api/v1/conversations/{id}/assign/{team_id}",
                "unassigned": "GET /api/v1/conversations/unassigned",
                "by_team": "GET /api/v1/conversations/team/{team_id}"
            },
            "messages": {
                "get": "GET /api/v1/messages/{id}",
                "delete": "DELETE /api/v1/messages/{id}",
                "restore": "PATCH /api/v1/messages/{id}/restore"
            },
            "media": {
                "upload": "POST /api/v1/media/upload",
                "upload_public": "POST /api/v1/media/upload/public",
                "list_assets": "GET /api/v1/media/assets",
                "get_asset": "GET /api/v1/media/assets/{id}",
                "delete_asset": "DELETE /api/v1/media/assets/{id}"
            },
            "websocket": {
                "chat": "WS /api/v1/ws/chat/{conversation_id}",
                "agent_notifications": "WS /api/v1/ws/agent-notifications"
            }
        },
        "documentation": {
            "swagger": "/docs",
            "redoc": "/redoc",
            "openapi_schema": "/openapi.json"
        }
    }

@app.get("/scalar", include_in_schema=False)
async def scalar_html():
    """Scalar API documentation"""
    return get_scalar_api_reference(
        openapi_url=app.openapi_url,
        title=app.title,
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host=settings.HOST, port=settings.PORT, reload=True)
