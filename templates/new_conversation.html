<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Conversation Alert - {{ app_name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
        }
        .alert-badge {
            background: #ff6b6b;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
        }
        .conversation-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        .detail-value {
            color: #333;
        }
        .action-button {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
        }
        .action-button:hover {
            background: #0056b3;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        .priority-high {
            color: #dc3545;
            font-weight: bold;
        }
        .priority-normal {
            color: #28a745;
        }
        .team-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ app_name }}</h1>
            <div class="alert-badge">🔔 New Conversation Alert</div>
        </div>

        <p>Hello {{ team_name or "Support Team" }},</p>
        
        <p>A new customer conversation has been created and assigned to your team. Please review the details below and respond promptly.</p>

        <div class="conversation-details">
            <h3>📋 Conversation Details</h3>
            
            <div class="detail-row">
                <span class="detail-label">Conversation ID:</span>
                <span class="detail-value">#{{ conversation_id }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Customer:</span>
                <span class="detail-value">{{ customer_name }}</span>
            </div>
            
            {% if customer_email %}
            <div class="detail-row">
                <span class="detail-label">Customer Email:</span>
                <span class="detail-value">{{ customer_email }}</span>
            </div>
            {% endif %}
            
            <div class="detail-row">
                <span class="detail-label">Created:</span>
                <span class="detail-value">{{ created_at }}</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Status:</span>
                <span class="detail-value">{{ status }}</span>
            </div>
            
            {% if priority %}
            <div class="detail-row">
                <span class="detail-label">Priority:</span>
                <span class="detail-value priority-{{ priority.lower() }}">{{ priority.upper() }}</span>
            </div>
            {% endif %}
        </div>

        {% if team_name %}
        <div class="team-info">
            <strong>👥 Assigned Team:</strong> {{ team_name }}
        </div>
        {% endif %}

        <div style="text-align: center;">
            <a href="{{ dashboard_url }}" class="action-button">
                🚀 Open Dashboard & Respond
            </a>
        </div>

        <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
            <strong>⚡ Quick Response Tips:</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Respond within 5 minutes for best customer experience</li>
                <li>Use the customer's name when possible</li>
                <li>Ask clarifying questions to understand their needs</li>
                <li>Provide clear next steps or solutions</li>
            </ul>
        </div>

        <div class="footer">
            <p>This is an automated notification from {{ app_name }}.</p>
            <p>If you have any questions, please contact your system administrator.</p>
            <p><small>Sent at {{ timestamp }}</small></p>
        </div>
    </div>
</body>
</html>
