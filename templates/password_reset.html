<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-M">
    <title>Password Reset Request - {{ app_name }}</title>
    <style>
        /* You can copy the excellent styles from your new_conversation.html template */
        body { font-family: sans-serif; max-width: 600px; margin: auto; padding: 20px; background-color: #f4f4f4; color: #333; }
        .container { background: white; padding: 30px; border-radius: 10px; }
        .header { text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 15px; margin-bottom: 25px; }
        .header h1 { color: #007bff; }
        .action-button { display: inline-block; background: #28a745; color: white !important; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #777; font-size: 12px; }
        .warning { font-size: 12px; color: #856404; background-color: #fff3cd; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Password Reset Request</h1>
        </div>

        <p>Hello {{ user_name }},</p>
        
        <p>We received a request to reset the password for your account associated with this email address. If you made this request, please click the button below to set a new password. This link is valid for **15 minutes**.</p>

        <div style="text-align: center;">
            <a href="{{ reset_link }}" class="action-button">
                Reset Your Password
            </a>
        </div>
        
        <p>If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>
        
        <p class="warning">
            <strong>Security Notice:</strong> This link is single-use and will expire in 15 minutes. Do not share it with anyone.
        </p>

        <div class="footer">
            <p>This is an automated notification from {{ app_name }}.</p>
        </div>
    </div>
</body>
</html>