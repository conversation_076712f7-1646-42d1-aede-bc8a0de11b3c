#!/usr/bin/env python3
"""
Demo script to show how to use both CU_TEST.py and AG_TEST.py together
This script demonstrates the complete flow of customer-agent chat testing
"""

import subprocess
import sys
import time
import os

def print_header(title):
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_step(step_num, description):
    print(f"\n📋 Step {step_num}: {description}")
    print("-" * 50)

def main():
    print_header("🚀 YUPCHA WEBSOCKET CHAT TESTING DEMO")
    
    print("""
This demo will guide you through testing the WebSocket chat functionality
between customers and agents using the provided test scripts.

Prerequisites:
✅ Server running on port 8000
✅ Python packages: websockets, aiohttp, aioconsole
✅ CU_TEST.py and AG_TEST.py files in current directory
    """)
    
    # Check if files exist
    if not os.path.exists("CU_TEST.py"):
        print("❌ CU_TEST.py not found in current directory")
        return
    
    if not os.path.exists("AG_TEST.py"):
        print("❌ AG_TEST.py not found in current directory")
        return
    
    print("✅ Test files found")
    
    print_step(1, "Start the Server")
    print("""
Make sure your server is running on port 8000:

    cd /home/<USER>/Documents/Devlopment/yupcha-customerbot-ai
    uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

Press Enter when the server is running...
    """)
    input()
    
    print_step(2, "Test Customer Client")
    print("""
Now we'll start the customer client. This will:
1. Create a new customer
2. Create a new conversation  
3. Connect to WebSocket
4. Wait for you to send messages

In a NEW TERMINAL WINDOW, run:

    python CU_TEST.py

IMPORTANT: Note the Conversation ID that appears in the output!
You'll need it for the agent client.

Example output:
    ✅ Setup complete. Conversation ID: 06874e73-0b6b-766d-8000-12dfb3637338

Press Enter when you have the customer client running and noted the Conversation ID...
    """)
    input()
    
    conversation_id = input("Enter the Conversation ID from the customer client: ").strip()
    
    print_step(3, "Test Agent Client")
    print(f"""
Now we'll start the agent client. This will:
1. Login as an agent
2. Ask for the conversation ID
3. Connect to the same conversation
4. Allow two-way chat

In ANOTHER NEW TERMINAL WINDOW, run:

    python AG_TEST.py

When prompted for the conversation ID, enter: {conversation_id}

Press Enter when you have the agent client running...
    """)
    input()
    
    print_step(4, "Test Two-Way Communication")
    print("""
Now you should have both clients running and connected to the same conversation.

Test the following:

1. In the CUSTOMER client, send a message:
   >> Your message: Hello, I need help with my order

2. In the AGENT client, you should see:
   >> [CUSTOMER]: Hello, I need help with my order

3. In the AGENT client, send a reply:
   >> Your reply: Hello! I'm here to help. What's the issue?

4. In the CUSTOMER client, you should see:
   >> [AGENT]: Hello! I'm here to help. What's the issue?

5. Continue the conversation to test real-time messaging

Press Enter when you've tested the two-way communication...
    """)
    input()
    
    print_step(5, "Test Disconnection and Reconnection")
    print("""
Test the connection handling:

1. In either client, type 'quit' and press Enter
2. Observe that the connection closes properly
3. You can restart that client and reconnect to continue the conversation

Press Enter when you've tested disconnection...
    """)
    input()
    
    print_step(6, "Test Archive Functionality (Optional)")
    print(f"""
You can also test the archive functionality using curl commands:

1. Archive the conversation:
   curl -X POST "http://localhost:8000/api/v1/conversations/{conversation_id}/archive" -b test_cookies.txt

2. List conversations (should be hidden):
   curl -X GET "http://localhost:8000/api/v1/conversations/" -b test_cookies.txt

3. List with archived (should appear):
   curl -X GET "http://localhost:8000/api/v1/conversations/?include_archived=true" -b test_cookies.txt

4. Unarchive the conversation:
   curl -X POST "http://localhost:8000/api/v1/conversations/{conversation_id}/unarchive" -b test_cookies.txt

Note: You'll need to login first to get the session cookie:
   curl -X POST "http://localhost:8000/api/v1/auth/login" -H "Content-Type: application/x-www-form-urlencoded" -d "username=<EMAIL>&password=adminpassword" -c test_cookies.txt

Press Enter to continue...
    """)
    input()
    
    print_header("🎉 TESTING COMPLETE")
    print("""
Congratulations! You've successfully tested the WebSocket chat functionality.

What you've verified:
✅ Customer can connect and send messages
✅ Agent can connect and send messages  
✅ Real-time two-way communication works
✅ Connection handling works properly
✅ Archive functionality is available

Next steps:
1. Integrate this functionality into your frontend
2. Add more features like typing indicators
3. Implement file sharing capabilities
4. Add message history and persistence

For more information, see:
- WEBSOCKET_TEST_GUIDE.md
- API_CONVERSATION_ARCHIVING.md
- README_CONVERSATION_ARCHIVING.md
    """)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
        sys.exit(1)
