"""add owner_id and is_public to asset

Revision ID: bf5d7125d5b8
Revises: 4e23f785f0e9
Create Date: 2025-07-06 21:36:38.684193

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'bf5d7125d5b8'
down_revision = '4e23f785f0e9'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assets', sa.Column('owner_id', sa.Integer(), nullable=True))
    op.add_column('assets', sa.Column('is_public', sa.<PERSON>(), nullable=True))
    op.create_foreign_key(None, 'assets', 'users', ['owner_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'assets', type_='foreignkey')
    op.drop_column('assets', 'is_public')
    op.drop_column('assets', 'owner_id')
    # ### end Alembic commands ###
