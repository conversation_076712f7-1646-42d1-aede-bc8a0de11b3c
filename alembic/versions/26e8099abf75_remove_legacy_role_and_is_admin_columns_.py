"""Remove legacy role and is_admin columns from user table

Revision ID: 26e8099abf75
Revises: e29a061d1205
Create Date: 2025-06-26 13:41:49.463477

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '26e8099abf75'
down_revision = 'e29a061d1205'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass

def downgrade() -> None:
    pass
