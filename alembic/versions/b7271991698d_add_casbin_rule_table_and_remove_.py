from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b7271991698d'
down_revision = '992cf53c2dba'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('casbin_rule',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('ptype', sa.String(length=255), nullable=True),
    sa.Column('v0', sa.String(length=255), nullable=True),
    sa.Column('v1', sa.String(length=255), nullable=True),
    sa.Column('v2', sa.String(length=255), nullable=True),
    sa.Column('v3', sa.String(length=255), nullable=True),
    sa.Column('v4', sa.String(length=255), nullable=True),
    sa.Column('v5', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('role_permission_association')
    op.drop_table('permissions')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by <PERSON><PERSON>bic - please adjust! ###
    op.create_table('permissions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('resource', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('action', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('is_system_permission', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', sa.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='permissions_pkey')
    )
    op.create_table('role_permission_association',
    sa.Column('role_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('permission_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], name='role_permission_association_permission_id_fkey'),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], name='role_permission_association_role_id_fkey'),
    sa.PrimaryKeyConstraint('role_id', 'permission_id', name='role_permission_association_pkey')
    )
    op.drop_table('casbin_rule')
    # ### end Alembic commands ###