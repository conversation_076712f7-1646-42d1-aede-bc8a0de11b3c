"""Add is_bot_active to conversations

Revision ID: b4e15ed243f4
Revises: 5d4863efee52
Create Date: 2025-07-15 17:49:23.849204

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b4e15ed243f4'
down_revision = '5d4863efee52'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('conversations', sa.Column('is_bot_active', sa.<PERSON>(), server_default='true', nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('conversations', 'is_bot_active')
    # ### end Alembic commands ###
