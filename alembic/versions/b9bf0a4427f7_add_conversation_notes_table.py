"""Add conversation notes table

Revision ID: b9bf0a4427f7
Revises: 689df9098c49
Create Date: 2025-07-02 15:21:30.688538

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b9bf0a4427f7'
down_revision = '689df9098c49'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('conversation_notes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('conversation_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_conversation_notes_conversation_id'), 'conversation_notes', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_conversation_notes_id'), 'conversation_notes', ['id'], unique=False)
    op.create_index(op.f('ix_conversation_notes_user_id'), 'conversation_notes', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_conversation_notes_user_id'), table_name='conversation_notes')
    op.drop_index(op.f('ix_conversation_notes_id'), table_name='conversation_notes')
    op.drop_index(op.f('ix_conversation_notes_conversation_id'), table_name='conversation_notes')
    op.drop_table('conversation_notes')
    # ### end Alembic commands ###
