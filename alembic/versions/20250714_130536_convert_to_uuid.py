"""Convert all primary and foreign keys to UUID

Revision ID: uuid_conversion
Revises: 
Create Date: 2025-01-14 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = 'uuid_conversion'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    """
    This migration will drop all existing tables and recreate them with UUID primary keys.
    This is a destructive migration that will delete all existing data.
    """
    
    # Drop all existing tables (this will delete all data)
    op.execute("DROP TABLE IF EXISTS conversation_label_association CASCADE")
    op.execute("DROP TABLE IF EXISTS user_role_association CASCADE")
    op.execute("DROP TABLE IF EXISTS activity_logs CASCADE")
    op.execute("DROP TABLE IF EXISTS conversation_notes CASCADE")
    op.execute("DROP TABLE IF EXISTS notes CASCADE")
    op.execute("DROP TABLE IF EXISTS labels CASCADE")
    op.execute("DROP TABLE IF EXISTS assets CASCADE")
    op.execute("DROP TABLE IF EXISTS messages CASCADE")
    op.execute("DROP TABLE IF EXISTS conversations CASCADE")
    op.execute("DROP TABLE IF EXISTS canned_responses CASCADE")
    op.execute("DROP TABLE IF EXISTS customers CASCADE")
    op.execute("DROP TABLE IF EXISTS users CASCADE")
    op.execute("DROP TABLE IF EXISTS teams CASCADE")
    op.execute("DROP TABLE IF EXISTS roles CASCADE")
    op.execute("DROP TABLE IF EXISTS organizations CASCADE")
    op.execute("DROP TABLE IF EXISTS casbin_rule CASCADE")
    
    print("✅ All existing tables dropped. New UUID-based tables will be created on next startup.")

def downgrade():
    """
    Downgrade is not supported for this migration as it involves 
    fundamental schema changes and data loss.
    """
    pass
