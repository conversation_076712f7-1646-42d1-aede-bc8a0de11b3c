"""Rename company_id to organization_id on User model

Revision ID: 3f5b09852fbc
Revises: fa603cf2b863
Create Date: 2025-07-15 19:24:55.920986

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3f5b09852fbc'
down_revision = 'fa603cf2b863'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands manually edited to rename column instead of drop/add ###
    # PostgreSQL syntax to rename column
    op.execute('ALTER TABLE users RENAME COLUMN company_id TO organization_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands manually edited to rename column back ###
    # PostgreSQL syntax to rename column back
    op.execute('ALTER TABLE users RENAME COLUMN organization_id TO company_id')
    # ### end Alembic commands ###
