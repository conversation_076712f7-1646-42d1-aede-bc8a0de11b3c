"""Add read_at and read_by to Message model

Revision ID: f3d67d94eccc
Revises: 3f5b09852fbc
Create Date: 2025-07-18 15:52:59.801737

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f3d67d94eccc'
down_revision = '3f5b09852fbc'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messages', sa.Column('read_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('messages', sa.Column('read_by_user_id', sa.UUID(), nullable=True))
    op.create_foreign_key(None, 'messages', 'users', ['read_by_user_id'], ['id'])
    op.drop_index(op.f('ix_users_company_id'), table_name='users')
    op.create_index(op.f('ix_users_organization_id'), 'users', ['organization_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_organization_id'), table_name='users')
    op.create_index(op.f('ix_users_company_id'), 'users', ['organization_id'], unique=False)
    op.drop_constraint(None, 'messages', type_='foreignkey')
    op.drop_column('messages', 'read_by_user_id')
    op.drop_column('messages', 'read_at')
    # ### end Alembic commands ###
