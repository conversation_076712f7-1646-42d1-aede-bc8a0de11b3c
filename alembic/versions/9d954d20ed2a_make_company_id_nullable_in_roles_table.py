"""Make company_id nullable in roles table

Revision ID: 9d954d20ed2a
Revises: 
Create Date: 2025-07-03 16:16:31.728924

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9d954d20ed2a'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('roles', 'company_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('roles', 'company_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###
