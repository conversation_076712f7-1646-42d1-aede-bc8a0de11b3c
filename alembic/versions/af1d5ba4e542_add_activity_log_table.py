from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'af1d5ba4e542'
down_revision = 'b7271991698d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('activity_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('user_id', sa.Integer(), nullable=True),
    sa.Column('user_email', sa.String(), nullable=True),
    sa.Column('action', sa.String(length=255), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('target_resource_type', sa.String(length=100), nullable=True),
    sa.Column('target_resource_id', sa.Integer(), nullable=True),
    sa.Column('details', sa.JSO<PERSON>(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_activity_logs_action'), 'activity_logs', ['action'], unique=False)
    op.create_index(op.f('ix_activity_logs_id'), 'activity_logs', ['id'], unique=False)
    op.create_index(op.f('ix_activity_logs_target_resource_id'), 'activity_logs', ['target_resource_id'], unique=False)
    op.create_index(op.f('ix_activity_logs_target_resource_type'), 'activity_logs', ['target_resource_type'], unique=False)
    op.create_index(op.f('ix_activity_logs_user_id'), 'activity_logs', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_activity_logs_user_id'), table_name='activity_logs')
    op.drop_index(op.f('ix_activity_logs_target_resource_type'), table_name='activity_logs')
    op.drop_index(op.f('ix_activity_logs_target_resource_id'), table_name='activity_logs')
    op.drop_index(op.f('ix_activity_logs_id'), table_name='activity_logs')
    op.drop_index(op.f('ix_activity_logs_action'), table_name='activity_logs')
    op.drop_table('activity_logs')
    # ### end Alembic commands ###