"""Add archived status to conversations

Revision ID: 5d4863efee52
Revises: 2ab15386ea36
Create Date: 2025-07-15 13:57:04.256777

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5d4863efee52'
down_revision = '2ab15386ea36'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # First, create the new enum type
    conversation_status_enum = postgresql.ENUM('new', 'open', 'closed', 'archived', name='conversation_status_enum')
    conversation_status_enum.create(op.get_bind())

    # Then alter the column to use the new enum type
    op.alter_column('conversations', 'status',
               existing_type=postgresql.ENUM('new', 'open', 'closed', name='conversationstatus'),
               type_=conversation_status_enum,
               existing_nullable=False,
               postgresql_using='status::text::conversation_status_enum')

    # Drop the old enum type
    old_enum = postgresql.ENUM('new', 'open', 'closed', name='conversationstatus')
    old_enum.drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # First, create the old enum type
    old_enum = postgresql.ENUM('new', 'open', 'closed', name='conversationstatus')
    old_enum.create(op.get_bind())

    # Then alter the column to use the old enum type
    op.alter_column('conversations', 'status',
               existing_type=sa.Enum('new', 'open', 'closed', 'archived', name='conversation_status_enum'),
               type_=old_enum,
               existing_nullable=False,
               postgresql_using="CASE WHEN status = 'archived' THEN 'closed'::text::conversationstatus ELSE status::text::conversationstatus END")

    # Drop the new enum type
    conversation_status_enum = postgresql.ENUM('new', 'open', 'closed', 'archived', name='conversation_status_enum')
    conversation_status_enum.drop(op.get_bind())
    # ### end Alembic commands ###
