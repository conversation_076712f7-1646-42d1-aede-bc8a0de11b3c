"""Add organization_id and team_id to users

Revision ID: e29a061d1205
Revises: 939eeda334a2
Create Date: 2025-06-25 16:27:02.711818

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e29a061d1205'
down_revision = '939eeda334a2'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_roles_name'), 'roles', ['name'], unique=True)
    op.drop_constraint(op.f('roles_company_id_fkey'), 'roles', type_='foreignkey')
    op.drop_column('roles', 'company_id')
    op.drop_constraint(op.f('user_role_association_assigned_by_fkey'), 'user_role_association', type_='foreignkey')
    op.drop_column('user_role_association', 'assigned_by')
    op.drop_column('user_role_association', 'assigned_at')
    op.add_column('users', sa.Column('organization_id', sa.Integer(), nullable=True))
    op.drop_index(op.f('ix_users_company_id'), table_name='users')
    op.create_index(op.f('ix_users_organization_id'), 'users', ['organization_id'], unique=False)
    op.drop_constraint(op.f('users_company_id_fkey'), 'users', type_='foreignkey')
    op.create_foreign_key(None, 'users', 'organizations', ['organization_id'], ['id'])
    op.drop_column('users', 'company_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('company_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.create_foreign_key(op.f('users_company_id_fkey'), 'users', 'organizations', ['company_id'], ['id'])
    op.drop_index(op.f('ix_users_organization_id'), table_name='users')
    op.create_index(op.f('ix_users_company_id'), 'users', ['company_id'], unique=False)
    op.drop_column('users', 'organization_id')
    op.add_column('user_role_association', sa.Column('assigned_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True))
    op.add_column('user_role_association', sa.Column('assigned_by', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key(op.f('user_role_association_assigned_by_fkey'), 'user_role_association', 'users', ['assigned_by'], ['id'])
    op.add_column('roles', sa.Column('company_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.create_foreign_key(op.f('roles_company_id_fkey'), 'roles', 'organizations', ['company_id'], ['id'])
    op.drop_index(op.f('ix_roles_name'), table_name='roles')
