"""Add target_resource_type to activity_logs table

Revision ID: fa603cf2b863
Revises: b4e15ed243f4
Create Date: 2025-07-15 18:19:51.829951

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fa603cf2b863'
down_revision = 'b4e15ed243f4'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('activity_logs', sa.Column('target_resource_type', sa.String(length=100), nullable=True))
    op.create_index(op.f('ix_activity_logs_target_resource_type'), 'activity_logs', ['target_resource_type'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_activity_logs_target_resource_type'), table_name='activity_logs')
    op.drop_column('activity_logs', 'target_resource_type')
    # ### end Alembic commands ###
