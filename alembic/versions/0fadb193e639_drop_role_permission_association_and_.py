"""Drop role_permission_association and permissions tables

Revision ID: 0fadb193e639
Revises: eb9f5e62da53
Create Date: 2025-06-27 17:37:39.241516

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0fadb193e639'
down_revision = 'eb9f5e62da53'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('role_permission_association')
    op.drop_table('permissions')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('permissions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('resource', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('action', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('description', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('is_system_permission', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', sa.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='permissions_pkey'),
    sa.UniqueConstraint('name', name='permissions_name_key')
    )
    op.create_table('role_permission_association',
    sa.Column('role_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('permission_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], name='role_permission_association_permission_id_fkey'),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], name='role_permission_association_role_id_fkey'),
    sa.PrimaryKeyConstraint('role_id', 'permission_id', name='role_permission_association_pkey')
    )
    # ### end Alembic commands ###
