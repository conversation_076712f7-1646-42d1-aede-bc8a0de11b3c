"""add profile image to user

Revision ID: 958e28e17d56
Revises: 8a7fe27298a7
Create Date: 2025-07-06 17:42:02.449682

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '958e28e17d56'
down_revision = '8a7fe27298a7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('profile_image_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'users', 'assets', ['profile_image_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_column('users', 'profile_image_id')
    # ### end Alembic commands ###
