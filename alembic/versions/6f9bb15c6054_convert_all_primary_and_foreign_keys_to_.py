"""Convert all primary and foreign keys to UUID

Revision ID: 6f9bb15c6054
Revises: d0da8a05b32b
Create Date: 2025-07-14 13:00:38.185795

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6f9bb15c6054'
down_revision = 'd0da8a05b32b'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('activity_logs', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('activity_logs', 'user_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=True)
    op.alter_column('activity_logs', 'target_resource_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=True)
    op.drop_index(op.f('ix_activity_logs_target_resource_type'), table_name='activity_logs')
    op.drop_column('activity_logs', 'target_resource_type')
    op.alter_column('assets', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('assets_id_seq'::regclass)"))
    op.alter_column('assets', 'owner_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=True)
    op.alter_column('canned_responses', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('canned_responses', 'organization_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('canned_responses', 'created_by',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('conversation_label_association', 'conversation_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('conversation_label_association', 'label_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('conversation_notes', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('conversation_notes', 'conversation_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('conversations', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('conversations', 'customer_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('conversations', 'organization_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('conversations', 'assigned_team_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=True)
    op.alter_column('customers', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('customers_id_seq'::regclass)"))
    op.alter_column('customers', 'organization_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('labels', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('labels_id_seq'::regclass)"))
    op.alter_column('labels', 'organization_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('messages', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('messages', 'conversation_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('messages', 'customer_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=True)
    op.alter_column('messages', 'user_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=True)
    op.alter_column('messages', 'asset_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=True)
    op.alter_column('notes', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('notes', 'customer_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('organizations', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('organizations_id_seq'::regclass)"))
    op.alter_column('organizations', 'default_team_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=True)
    op.alter_column('roles', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('roles_id_seq'::regclass)"))
    op.alter_column('roles', 'company_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('teams', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('teams_id_seq'::regclass)"))
    op.alter_column('teams', 'organization_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('user_role_association', 'user_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('user_role_association', 'role_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False)
    op.alter_column('users', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('users_id_seq'::regclass)"))
    op.alter_column('users', 'company_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=True)
    op.alter_column('users', 'team_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=True)
    op.alter_column('users', 'role_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=True)
    op.alter_column('users', 'profile_image_id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'profile_image_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('users', 'role_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('users', 'team_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('users', 'company_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('users', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('users_id_seq'::regclass)"))
    op.alter_column('user_role_association', 'role_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('user_role_association', 'user_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('teams', 'organization_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('teams', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('teams_id_seq'::regclass)"))
    op.alter_column('roles', 'company_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('roles', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('roles_id_seq'::regclass)"))
    op.alter_column('organizations', 'default_team_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('organizations', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('organizations_id_seq'::regclass)"))
    op.alter_column('notes', 'customer_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('notes', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('messages', 'asset_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('messages', 'user_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('messages', 'customer_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('messages', 'conversation_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('messages', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('labels', 'organization_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('labels', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('labels_id_seq'::regclass)"))
    op.alter_column('customers', 'organization_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('customers', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('customers_id_seq'::regclass)"))
    op.alter_column('conversations', 'assigned_team_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('conversations', 'organization_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('conversations', 'customer_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('conversations', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('conversation_notes', 'conversation_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('conversation_notes', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('conversation_label_association', 'label_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('conversation_label_association', 'conversation_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('canned_responses', 'created_by',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('canned_responses', 'organization_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('canned_responses', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('assets', 'owner_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('assets', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               existing_server_default=sa.text("nextval('assets_id_seq'::regclass)"))
    op.add_column('activity_logs', sa.Column('target_resource_type', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.create_index(op.f('ix_activity_logs_target_resource_type'), 'activity_logs', ['target_resource_type'], unique=False)
    op.alter_column('activity_logs', 'target_resource_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('activity_logs', 'user_id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('activity_logs', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    # ### end Alembic commands ###
