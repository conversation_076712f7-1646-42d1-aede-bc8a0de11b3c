"""abac refactor: company_id, dynamic roles, permissions

Revision ID: 7511bd013fb0
Revises: 10e6834f22f9
Create Date: 2025-06-23 19:56:28.515471

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7511bd013fb0'
down_revision = '10e6834f22f9'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('permissions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('resource', sa.String(), nullable=False),
    sa.Column('action', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('is_system_permission', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_permissions_action'), 'permissions', ['action'], unique=False)
    op.create_index(op.f('ix_permissions_id'), 'permissions', ['id'], unique=False)
    op.create_index(op.f('ix_permissions_name'), 'permissions', ['name'], unique=True)
    op.create_index(op.f('ix_permissions_resource'), 'permissions', ['resource'], unique=False)
    op.create_table('role_permission_association',
    sa.Column('role_id', sa.Integer(), nullable=False),
    sa.Column('permission_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.PrimaryKeyConstraint('role_id', 'permission_id')
    )
    op.add_column('roles', sa.Column('company_id', sa.Integer(), nullable=False))
    op.drop_index('ix_roles_name', table_name='roles')
    op.create_foreign_key(None, 'roles', 'organizations', ['company_id'], ['id'])
    op.add_column('users', sa.Column('company_id', sa.Integer(), nullable=True))
    op.drop_index('ix_users_organization_id', table_name='users')
    op.create_index(op.f('ix_users_company_id'), 'users', ['company_id'], unique=False)
    op.drop_constraint('users_organization_id_fkey', 'users', type_='foreignkey')
    op.create_foreign_key(None, 'users', 'organizations', ['company_id'], ['id'])
    op.drop_column('users', 'organization_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('organization_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.create_foreign_key('users_organization_id_fkey', 'users', 'organizations', ['organization_id'], ['id'])
    op.drop_index(op.f('ix_users_company_id'), table_name='users')
    op.create_index('ix_users_organization_id', 'users', ['organization_id'], unique=False)
    op.drop_column('users', 'company_id')
    op.drop_constraint(None, 'roles', type_='foreignkey')
    op.create_index('ix_roles_name', 'roles', ['name'], unique=False)
    op.drop_column('roles', 'company_id')
    op.drop_table('role_permission_association')
    op.drop_index(op.f('ix_permissions_resource'), table_name='permissions')
    op.drop_index(op.f('ix_permissions_name'), table_name='permissions')
    op.drop_index(op.f('ix_permissions_id'), table_name='permissions')
    op.drop_index(op.f('ix_permissions_action'), table_name='permissions')
    op.drop_table('permissions')
    # ### end Alembic commands ###
