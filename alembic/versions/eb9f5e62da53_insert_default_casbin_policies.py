"""Insert default Casbin policies

Revision ID: eb9f5e62da53
Revises: 26e8099abf75
Create Date: 2025-06-27 10:26:12.394875

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'eb9f5e62da53'
down_revision = '26e8099abf75'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Read policies from casbin_policy.csv and insert them into casbin_rule table
    policy_file_path = "./app/casbin/casbin_policy.csv"
    policies = []
    with open(policy_file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            parts = line.split(', ')
            policy_data = {'ptype': parts[0]}
            # Initialize all v-columns to None
            for i in range(6):
                policy_data[f'v{i}'] = None
            # Populate with actual values from CSV
            for i, part in enumerate(parts[1:]):
                policy_data[f'v{i}'] = part
            policies.append(policy_data)

    # Define the table with all possible v columns, allowing them to be nullable
    casbin_rule_table = sa.Table(
        'casbin_rule',
        sa.MetaData(),
        sa.Column('id', sa.Integer, primary_key=True),
        sa.Column('ptype', sa.String(255)),
        sa.Column('v0', sa.String(255), nullable=True),
        sa.Column('v1', sa.String(255), nullable=True),
        sa.Column('v2', sa.String(255), nullable=True),
        sa.Column('v3', sa.String(255), nullable=True),
        sa.Column('v4', sa.String(255), nullable=True),
        sa.Column('v5', sa.String(255), nullable=True),
    )

    if policies:
        op.bulk_insert(casbin_rule_table, policies)


def downgrade() -> None:
    pass

