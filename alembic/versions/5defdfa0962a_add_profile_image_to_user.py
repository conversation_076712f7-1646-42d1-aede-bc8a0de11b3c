"""add_profile_image_to_user
Revision ID: 5defdfa0962a
Revises: bf5d7125d5b8
Create Date: 2025-07-09 13:31:17.464908
"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5defdfa0962a'
down_revision = 'bf5d7125d5b8'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('profile_image_id', sa.Integer(), nullable=True))
    op.create_foreign_key('fk_users_profile_image_id_assets', 'users', 'assets', ['profile_image_id'], ['id'], ondelete='SET NULL')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_users_profile_image_id_assets', 'users', type_='foreignkey')
    op.drop_column('users', 'profile_image_id')
    # ### end Alembic commands ###