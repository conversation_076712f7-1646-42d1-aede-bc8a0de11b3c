"""add ondelete set null to profile_image_id

Revision ID: 4e23f785f0e9
Revises: 958e28e17d56
Create Date: 2025-07-06 21:33:28.294012

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4e23f785f0e9'
down_revision = '958e28e17d56'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('users_profile_image_id_fkey', 'users', type_='foreignkey')
    op.create_foreign_key(None, 'users', 'assets', ['profile_image_id'], ['id'], ondelete='SET NULL')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.create_foreign_key('users_profile_image_id_fkey', 'users', 'assets', ['profile_image_id'], ['id'])
    # ### end Alembic commands ###
