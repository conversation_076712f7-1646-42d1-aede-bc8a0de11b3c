"""Add conversation labels and tags feature

Revision ID: 39dabed2413f
Revises: b5f0e5ec9960
Create Date: 2025-06-30 15:30:51.973717

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '39dabed2413f'
down_revision = 'b5f0e5ec9960'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('labels',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('color', sa.String(length=7), nullable=False),
    sa.Column('organization_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['organization_id'], ['organizations.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name', 'organization_id', name='_organization_label_name_uc')
    )
    op.create_index(op.f('ix_labels_id'), 'labels', ['id'], unique=False)
    op.create_index(op.f('ix_labels_name'), 'labels', ['name'], unique=False)
    op.create_index(op.f('ix_labels_organization_id'), 'labels', ['organization_id'], unique=False)
    op.create_table('conversation_label_association',
    sa.Column('conversation_id', sa.Integer(), nullable=False),
    sa.Column('label_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ),
    sa.ForeignKeyConstraint(['label_id'], ['labels.id'], ),
    sa.PrimaryKeyConstraint('conversation_id', 'label_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('conversation_label_association')
    op.drop_index(op.f('ix_labels_organization_id'), table_name='labels')
    op.drop_index(op.f('ix_labels_name'), table_name='labels')
    op.drop_index(op.f('ix_labels_id'), table_name='labels')
    op.drop_table('labels')
    # ### end Alembic commands ###
