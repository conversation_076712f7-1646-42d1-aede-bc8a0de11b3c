"""Add user_agent to activity log

Revision ID: 8a7fe27298a7
Revises: af1d5ba4e542
Create Date: 2025-07-06 14:06:44.566121

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8a7fe27298a7'
down_revision = 'af1d5ba4e542'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('activity_logs', sa.Column('user_agent', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('activity_logs', 'user_agent')
    # ### end Alembic commands ###