"""cleanup_user_model

Revision ID: 323325f15a38
Revises: 5defdfa0962a
Create Date: 2025-07-09 13:51:43.337349

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '323325f15a38'
down_revision = '5defdfa0962a'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Get the first organization's ID to use as a default
    conn = op.get_bind()
    result = conn.execute(sa.text("SELECT id FROM organizations ORDER BY id LIMIT 1"))
    first_org_id = result.scalar()

    # If there is an organization, update null company_ids in roles
    if first_org_id:
        op.execute(
            sa.text("UPDATE roles SET company_id = :org_id WHERE company_id IS NULL").bindparams(org_id=first_org_id)
        )

    op.alter_column('roles', 'company_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('roles', 'company_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###