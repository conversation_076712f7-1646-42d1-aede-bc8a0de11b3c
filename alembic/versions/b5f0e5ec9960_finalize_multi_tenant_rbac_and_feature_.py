"""Finalize multi-tenant RBAC and feature models

Revision ID: b5f0e5ec9960
Revises: 177cbebbed22
Create Date: 2025-06-30 12:00:50.069910

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b5f0e5ec9960'
down_revision = '177cbebbed22'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('roles', sa.Column('company_id', sa.Integer(), nullable=True))
    op.execute("UPDATE roles SET company_id = 1 WHERE company_id IS NULL") # Assuming default organization_id is 1
    op.alter_column('roles', 'company_id', existing_type=sa.Integer(), nullable=False, type_=sa.Integer(), postgresql_using='company_id::integer')
    op.drop_index(op.f('ix_roles_name'), table_name='roles')
    op.create_index(op.f('ix_roles_name'), 'roles', ['name'], unique=False)
    op.create_unique_constraint('_company_role_name_uc', 'roles', ['name', 'company_id'])
    op.create_index(op.f('ix_roles_company_id'), 'roles', ['company_id'], unique=False)
    op.create_foreign_key(None, 'roles', 'organizations', ['company_id'], ['id'])
    op.add_column('users', sa.Column('company_id', sa.Integer(), nullable=True))
    op.execute("UPDATE users SET company_id = organization_id")
    op.drop_index(op.f('ix_users_organization_id'), table_name='users')
    op.create_index(op.f('ix_users_company_id'), 'users', ['company_id'], unique=False)
    op.drop_constraint(op.f('users_organization_id_fkey'), 'users', type_='foreignkey')
    op.create_foreign_key(None, 'users', 'organizations', ['company_id'], ['id'])
    op.drop_column('users', 'organization_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('organization_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.create_foreign_key(op.f('users_organization_id_fkey'), 'users', 'organizations', ['organization_id'], ['id'])
    op.drop_index(op.f('ix_users_company_id'), table_name='users')
    op.create_index(op.f('ix_users_organization_id'), 'users', ['organization_id'], unique=False)
    op.drop_column('users', 'company_id')
    op.drop_constraint(None, 'roles', type_='foreignkey')
    op.drop_index(op.f('ix_roles_company_id'), table_name='roles')
    op.drop_constraint('_company_role_name_uc', 'roles', type_='unique')
    op.drop_index(op.f('ix_roles_name'), table_name='roles')
    op.create_index(op.f('ix_roles_name'), 'roles', ['name'], unique=True)
    op.drop_column('roles', 'company_id')
