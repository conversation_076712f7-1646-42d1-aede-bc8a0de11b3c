-- Initialize the database for Yupcha Customer Bot AI
-- This script is run when the PostgreSQL container starts for the first time

-- Create the database if it doesn't exist (this is handled by POSTGRES_DB env var)
-- CREATE DATABASE IF NOT EXISTS yupcha_chatbot;

-- Grant all privileges to the user
GRANT ALL PRIVILEGES ON DATABASE yupcha_chatbot TO yupcha_user;

-- Connect to the database
\c yupcha_chatbot;

-- <PERSON> schema privileges
GRANT ALL ON SCHEMA public TO yupcha_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO yupcha_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO yupcha_user;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO yupcha_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO yupcha_user;
