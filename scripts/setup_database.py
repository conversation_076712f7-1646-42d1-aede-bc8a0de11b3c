#!/usr/bin/env python3
"""
Database setup script for Yupcha Customer Bot AI.
This script creates the PostgreSQL database and user if they don't exist.
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from app.core.config import settings

def create_database():
    """Create the PostgreSQL database and user if they don't exist"""
    
    # Connection parameters for the default postgres database
    conn_params = {
        'host': settings.db_host,
        'port': settings.db_port,
        'user': 'postgres',  # Default superuser
        'password': input("Enter PostgreSQL superuser password: "),
        'database': 'postgres'  # Connect to default database
    }
    
    try:
        # Connect to PostgreSQL server
        print("Connecting to PostgreSQL server...")
        conn = psycopg2.connect(**conn_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Check if user exists
        cursor.execute(
            "SELECT 1 FROM pg_roles WHERE rolname = %s",
            (settings.db_user,)
        )
        user_exists = cursor.fetchone()
        
        if not user_exists:
            print(f"Creating user '{settings.db_user}'...")
            cursor.execute(
                f"CREATE USER {settings.db_user} WITH PASSWORD %s",
                (settings.db_password,)
            )
            print(f"User '{settings.db_user}' created successfully!")
        else:
            print(f"User '{settings.db_user}' already exists.")
        
        # Check if database exists
        cursor.execute(
            "SELECT 1 FROM pg_database WHERE datname = %s",
            (settings.db_name,)
        )
        db_exists = cursor.fetchone()
        
        if not db_exists:
            print(f"Creating database '{settings.db_name}'...")
            cursor.execute(f"CREATE DATABASE {settings.db_name} OWNER {settings.db_user}")
            print(f"Database '{settings.db_name}' created successfully!")
        else:
            print(f"Database '{settings.db_name}' already exists.")
        
        # Grant privileges
        cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {settings.db_name} TO {settings.db_user}")
        print(f"Privileges granted to '{settings.db_user}' on database '{settings.db_name}'.")
        
        cursor.close()
        conn.close()
        
        print("\nDatabase setup completed successfully!")
        print(f"Database URL: {settings.database_url}")
        
    except psycopg2.Error as e:
        print(f"Error setting up database: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nSetup cancelled by user.")
        sys.exit(1)

if __name__ == "__main__":
    print("PostgreSQL Database Setup for Yupcha Customer Bot AI")
    print("=" * 50)
    print(f"Host: {settings.db_host}")
    print(f"Port: {settings.db_port}")
    print(f"Database: {settings.db_name}")
    print(f"User: {settings.db_user}")
    print()
    
    create_database()
