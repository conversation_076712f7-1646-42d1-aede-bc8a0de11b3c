#!/usr/bin/env python3
"""
<PERSON>ript to update existing user passwords to use proper hashing
"""

import sys
import os
import asyncio

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.security import get_password_hash
from app.db.session import AsyncSessionLocal
from app.crud import crud_user

async def update_passwords():
    """Update existing user passwords to use proper hashing"""
    async with AsyncSessionLocal() as db:
        try:
            # Update admin password
            admin = await crud_user.get_user_by_email(db, '<EMAIL>')
            if admin:
                admin.hashed_password = get_password_hash('adminpassword')
                await db.commit()
                print('✅ Admin password updated')
            
            # Update agent password
            '''
            agent = await crud_user.get_user_by_email(db, '<EMAIL>')
            if agent:
                agent.hashed_password = get_password_hash('agentpassword')
                await db.commit()
                print('✅ Agent password updated')
                
            print('🎉 All passwords updated successfully!')
            '''
            
        except Exception as e:
            print(f'❌ Error updating passwords: {e}')
            await db.rollback()

if __name__ == "__main__":
    asyncio.run(update_passwords())
