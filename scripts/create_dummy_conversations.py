import asyncio
import random
from datetime import datetime, timedelta
from faker import Faker
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_async_db
from app.models.user import User
from app.models.customer import Customer
from app.models.organization import Organization
from app.models.team import Team
from app.models.chat import Conversation, Message # Assuming these models exist in app/models/chat.py

fake = Faker()

async def create_dummy_conversations(num_conversations: int = 50):
    async for db in get_async_db():
        users_result = await db.execute(select(User).filter(User.is_active == True, User.is_deleted == False))
        users = users_result.scalars().all()

        customers_result = await db.execute(select(Customer))
        customers = customers_result.scalars().all()

        orgs_result = await db.execute(select(Organization)) # Fetch organizations
        orgs = orgs_result.scalars().all()

        teams_result = await db.execute(select(Team))
        teams = teams_result.scalars().all()

        if not users:
            print("No active users found. Please create some users first.")
            return
        if not customers:
            print("No customers found. Please create some customers first.")
            return
        if not orgs:
            print("No organizations found. Please create some organizations first.")
            return
        if not teams:
            print("No teams found. Please create some teams first.")
            return

        print(f"Creating {num_conversations} dummy conversations...")

        for _ in range(num_conversations):
            customer = random.choice(customers)
            agent = random.choice(users) # Agent is a user
            team = random.choice(teams) # Assign a team
            organization = random.choice(orgs) # Assign an organization

            conversation_start_time = fake.date_time_between(start_date="-1y", end_date="now")
            conversation_end_time = conversation_start_time + timedelta(minutes=random.randint(5, 120))

            conversation = Conversation(
                customer_id=customer.id,
                organization_id=organization.id,
                assigned_team_id=team.id,
                status=random.choice(["new", "open", "closed"]),
                created_at=conversation_start_time,
                updated_at=conversation_end_time
            )
            db.add(conversation)
            await db.flush() # Flush to get conversation.id

            # Create messages for the conversation
            num_messages = random.randint(5, 20)
            for i in range(num_messages):
                sender_is_customer = random.choice([True, False])

                message_time = conversation_start_time + timedelta(minutes=i * random.randint(1, 5))
                if message_time > datetime.now(): # Don't create messages in the future
                    message_time = datetime.now() - timedelta(seconds=random.randint(1, 60))

                if sender_is_customer:
                    message = Message(
                        conversation_id=conversation.id,
                        customer_id=customer.id,
                        user_id=None, # No user for customer messages
                        sender="customer",
                        content=fake.sentence(nb_words=random.randint(5, 15)),
                        message_type=random.choice(["text", "image", "file"]),
                        created_at=message_time
                    )
                else:
                    message = Message(
                        conversation_id=conversation.id,
                        customer_id=None, # No customer for agent messages
                        user_id=agent.id,
                        sender="agent",
                        content=fake.sentence(nb_words=random.randint(5, 15)),
                        message_type=random.choice(["text", "image", "file"]),
                        created_at=message_time
                    )
                db.add(message)

        await db.commit()
        print(f"Successfully created {num_conversations} dummy conversations with messages.")

if __name__ == "__main__":
    asyncio.run(create_dummy_conversations())