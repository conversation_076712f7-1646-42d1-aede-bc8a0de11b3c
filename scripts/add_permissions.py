import asyncio
from app.core.permissions import enforcer

# This script should be run once to populate the database with initial policies.
# Note: This will clear existing policies before adding new ones.

def setup_permissions():
    """
    Connects to the database, clears existing policies, and adds the default set of policies.
    This is a SYNCHRONOUS operation.
    """
    # Clear all existing policies to ensure a clean slate
    enforcer.remove_filtered_policy(0)
    print("Cleared all existing Casbin policies.")

    # Define the default policies for different roles
    default_policies = [
        # Admin: Full access to all major resources across all companies
        ("p", "Admin", "company:*:users", "read"),
        ("p", "Admin", "company:*:users", "write"),
        ("p", "Admin", "company:*:organizations", "read"),
        ("p", "Admin", "company:*:organizations", "write"),
        ("p", "Admin", "company:*:teams", "read"),
        ("p", "Admin", "company:*:teams", "write"),
        ("p", "Admin", "company:*:roles", "read"),
        ("p", "Admin", "company:*:roles", "write"),
        ("p", "Admin", "company:*:permissions", "read"),
        ("p", "Admin", "company:*:permissions", "write"),
        ("p", "Admin", "company:*:canned_responses", "read"),
        ("p", "Admin", "company:*:canned_responses", "write"),
        ("p", "Admin", "company:*:assets", "read"),
        ("p", "Admin", "company:*:assets", "write"),
        ("p", "Admin", "company:*:conversations", "read"),
        ("p", "Admin", "company:*:conversations", "write"),
        ("p", "Admin", "company:*:messages", "read"),
        ("p", "Admin", "company:*:messages", "write"),
        ("p", "Admin", "company:*:customers", "read"),
        ("p", "Admin", "company:*:customers", "write"),
        ("p", "Admin", "company:*:notifications", "read"),
        ("p", "Admin", "company:*:notifications", "write"),

        # Admin: Permissions for managing roles and user-role assignments
        ("p", "Admin", "roles", "create"),
        ("p", "Admin", "roles", "read"),
        ("p", "Admin", "roles", "update"),
        ("p", "Admin", "roles", "delete"),
        ("p", "Admin", "permissions", "read"),
        ("p", "Admin", "permissions", "write"),
        ("p", "Admin", "user_roles", "assign"),
        ("p", "Admin", "user_roles", "unassign"),
        ("p", "Admin", "user_roles", "read"),

        # Agent: Company-scoped permissions.
        ("p", "Agent", "company:{company_id}:conversations", "read"),
        ("p", "Agent", "company:{company_id}:conversations", "write"),
        ("p", "Agent", "company:{company_id}:messages", "read"),
        ("p", "Agent", "company:{company_id}:messages", "write"),
        ("p", "Agent", "company:{company_id}:customers", "read"),
        ("p", "Agent", "company:{company_id}:customers", "write"),
        ("p", "Agent", "company:{company_id}:canned_responses", "read"),
        ("p", "Agent", "company:{company_id}:notifications", "read"),
        ("p", "Agent", "company:{company_id}:assets", "read"),
    ]

    # Add all policies to the enforcer
    for p_type, sub, obj, act in default_policies:
        enforcer.add_policy(sub, obj, act)
    
    print(f"Added {len(default_policies)} policies to the enforcer.")

    # Save the newly added policies to the database
    enforcer.save_policy()
    print("Permissions saved to the database successfully.")

def main():
    setup_permissions()

if __name__ == "__main__":
    print("Running permission setup...")
    main()
    print("Permission setup finished.")