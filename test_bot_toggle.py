#!/usr/bin/env python3
"""
Simple test script to test bot toggle functionality
"""

import asyncio
import websockets
import json
import aiohttp

# Configuration
WS_BASE_URL = "ws://localhost:8000/api/v1/ws"
API_BASE_URL = "http://localhost:8000/api/v1"
CONVERSATION_ID = "0687649a-479a-722b-8000-4475447b12b5"
CUSTOMER_ID = "test-customer-bot-toggle"

async def test_bot_toggle():
    print("🧪 Testing Bot Toggle Functionality")
    print("=" * 50)
    
    # Connect as customer to the existing conversation
    uri = f"{WS_BASE_URL}/chat/{CONVERSATION_ID}?customer_id={CUSTOMER_ID}"
    print(f"Connecting to: {uri}")
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to conversation")
            
            # Send a test message
            test_message = {
                "type": "text",
                "content": "Testing bot toggle - bot should NOT respond to this"
            }
            
            await websocket.send(json.dumps(test_message))
            print("📤 Sent test message")
            
            # Wait for responses
            print("⏳ Waiting for responses...")
            timeout = 3  # Wait 3 seconds for bot response
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=timeout)
                data = json.loads(response)
                print(f"📥 Received: {data}")
                
                if data.get('sender') == 'bot':
                    print("❌ FAIL: Bot responded when it should be disabled!")
                else:
                    print("✅ PASS: Received non-bot message")
                    
                # Wait for potential bot response
                try:
                    bot_response = await asyncio.wait_for(websocket.recv(), timeout=2)
                    bot_data = json.loads(bot_response)
                    if bot_data.get('sender') == 'bot':
                        print("❌ FAIL: Bot responded when it should be disabled!")
                    else:
                        print("✅ PASS: No bot response received")
                except asyncio.TimeoutError:
                    print("✅ PASS: No bot response received (timeout)")
                    
            except asyncio.TimeoutError:
                print("✅ PASS: No response received (bot is disabled)")
                
    except Exception as e:
        print(f"❌ Connection failed: {e}")

async def test_enable_bot():
    print("\n🔄 Testing Bot Re-enable")
    print("=" * 30)
    
    # Enable bot via API
    async with aiohttp.ClientSession() as session:
        # Login first
        login_data = {
            "username": "<EMAIL>",
            "password": "adminpassword"
        }
        
        async with session.post(f"{API_BASE_URL}/auth/login", data=login_data) as resp:
            if resp.status != 200:
                print(f"❌ Login failed: {await resp.text()}")
                return
            
            # Enable bot
            toggle_data = {"active": True}
            async with session.post(
                f"{API_BASE_URL}/conversations/{CONVERSATION_ID}/toggle-bot",
                json=toggle_data
            ) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ Bot enabled: is_bot_active = {data['is_bot_active']}")
                else:
                    print(f"❌ Failed to enable bot: {await resp.text()}")
                    return
    
    # Test that bot now responds
    uri = f"{WS_BASE_URL}/chat/{CONVERSATION_ID}?customer_id={CUSTOMER_ID}-2"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to conversation")
            
            # Send a test message
            test_message = {
                "type": "text",
                "content": "Testing bot toggle - bot SHOULD respond to this"
            }
            
            await websocket.send(json.dumps(test_message))
            print("📤 Sent test message")
            
            # Wait for bot response
            print("⏳ Waiting for bot response...")
            timeout = 5  # Wait 5 seconds for bot response
            
            bot_responded = False
            try:
                while True:
                    response = await asyncio.wait_for(websocket.recv(), timeout=timeout)
                    data = json.loads(response)
                    print(f"📥 Received: {data}")
                    
                    if data.get('sender') == 'bot':
                        print("✅ PASS: Bot responded when enabled!")
                        bot_responded = True
                        break
                        
            except asyncio.TimeoutError:
                if not bot_responded:
                    print("❌ FAIL: Bot did not respond when it should be enabled")
                    
    except Exception as e:
        print(f"❌ Connection failed: {e}")

async def main():
    print("🤖 Bot Toggle Feature Test")
    print("=" * 50)
    
    # Test 1: Bot should be disabled
    await test_bot_toggle()
    
    # Test 2: Re-enable bot and test
    await test_enable_bot()
    
    print("\n🏁 Test completed!")

if __name__ == "__main__":
    asyncio.run(main())
