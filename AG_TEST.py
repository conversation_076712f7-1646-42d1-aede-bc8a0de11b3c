# yupcha-customerbot-ai/tests/demo_agent_chat.py

import asyncio
import websockets
import json
import aioconsole
import aiohttp
import ssl

# --- Configuration ---
WS_BASE_URL = "ws://localhost:8000/api/v1/ws"
API_BASE_URL = "http://localhost:8000/api/v1"
AGENT_EMAIL = "<EMAIL>"
AGENT_PASSWORD = "adminpassword"
ORGANIZATION_ID = '06874bc0-ac71-75ea-8000-8666ab57b647'

async def login_agent():
    """Login as an agent and get session cookie"""
    print("Logging in as agent...")
    async with aiohttp.ClientSession() as session:
        login_data = {
            "username": AGENT_EMAIL,
            "password": AGENT_PASSWORD
        }

        async with session.post(f"{API_BASE_URL}/auth/login", data=login_data) as resp:
            if resp.status != 200:
                print(f"❌ Login failed: {await resp.text()}")
                return None

            # Extract session cookie
            cookies = resp.cookies
            session_cookie = {cookie.key: cookie.value for cookie in cookies.values()}

            print("✅ Agent login successful")
            return session_cookie

async def get_conversation_id():
    """Get a conversation ID to join"""
    conversation_id = await aioconsole.ainput("Enter conversation ID to join: ")
    return conversation_id.strip()
async def run_chat_client(websocket):
    """Handles sending and receiving messages for a connected client."""
    async def receive_messages():
        try:
            async for message in websocket:
                if message.type == aiohttp.WSMsgType.TEXT:
                    data = json.loads(message.data)
                    sender = data.get('sender', 'system')
                    content = data.get('content', data.get('detail', 'No content'))

                    # Blue for received messages from customer
                    print(f"\n>> [{sender.upper()}]: {content}")
                    print(">> Your reply: ", end="", flush=True)
                elif message.type == aiohttp.WSMsgType.ERROR:
                    print(f"\nConnection closed with exception {websocket.exception()}")
                    break
        except asyncio.CancelledError:
            # This is expected on shutdown
            pass
        except Exception as e:
            print(f"\nError receiving message: {e}")

    async def send_messages():
        while True:
            message_to_send = await aioconsole.ainput(">> Your reply: ")
            if message_to_send.lower() in ['quit', 'exit', 'q']:
                break

            try:
                await websocket.send_str(json.dumps({
                    "type": "text",
                    "content": message_to_send
                }))
            except Exception as e:
                print(f"Error sending message: {e}")
                break

    receiver_task = asyncio.create_task(receive_messages())
    await send_messages()
    receiver_task.cancel()


async def main():
    # Login first
    session_cookie = await login_agent()
    if not session_cookie:
        return

    # Get conversation ID to join
    conversation_id = await get_conversation_id()
    if not conversation_id:
        return

    # Create WebSocket URI for agent - use the same endpoint as customers but with session cookie
    uri = f"{WS_BASE_URL}/chat/{conversation_id}"

    print("\n" + "="*50)
    print("🚀 Yupcha Interactive Agent Chat Client")
    print(f"👤 Agent: {AGENT_EMAIL}")
    print(f"💬 Conversation ID: {conversation_id}")
    print("--------------------------------------------------")
    print("Type a message and press Enter. Type 'quit' to exit.")
    print("="*50 + "\n")

    try:
        print("Attempting to connect to WebSocket...")
        print(f"URI: {uri}")
        print(f"Cookie: {session_cookie.get('cookie', 'None')}")

        async with aiohttp.ClientSession(cookies=session_cookie) as session:
            async with session.ws_connect(uri) as websocket:
                print("✅ Connected to conversation")
                await run_chat_client(websocket)
    except Exception as e:
        print(f"\n❌ Connection failed: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure the server is running on port 8000")
        print("2. Verify the conversation ID is correct")
        print("3. Check if the WebSocket endpoint exists")
    finally:
        print("\n⏹️ Agent client finished.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Disconnecting...")
