#!/usr/bin/env python3
"""
WebSocket Test Script for Single Role System
Tests that any authenticated user (Ad<PERSON>, HR, Manager, etc.) can chat with customers
"""

import asyncio
import websockets
import json
import requests
import sys
from typing import Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebSocketTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.ws_url = base_url.replace("http", "ws")
        self.session = requests.Session()
        self.conversation_id = 1  # Test conversation ID
        
    def login(self, email: str, password: str) -> bool:
        """Login and get session cookie"""
        try:
            response = self.session.post(
                f"{self.base_url}/api/v1/auth/login",
                data={"username": email, "password": password},
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            )
            
            if response.status_code == 200:
                logger.info(f"✅ Login successful for {email}")
                return True
            else:
                logger.error(f"❌ Login failed for {email}: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Login error for {email}: {e}")
            return False
    
    def get_current_user(self) -> Optional[dict]:
        """Get current user info"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/auth/me")
            if response.status_code == 200:
                user_data = response.json()
                logger.info(f"✅ Current user: {user_data['full_name']} ({user_data['email']})")
                logger.info(f"   Role: {user_data['role']['name'] if user_data['role'] else 'Agent (default)'}")
                logger.info(f"   Admin: {user_data.get('is_admin', False)}")
                return user_data
            else:
                logger.error(f"❌ Failed to get user info: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"❌ Error getting user info: {e}")
            return None
    
    def get_session_cookie(self) -> Optional[str]:
        """Extract session cookie for WebSocket"""
        cookies = self.session.cookies
        logger.info(f"Available cookies: {[(c.name, c.value[:20] + '...' if len(c.value) > 20 else c.value) for c in cookies]}")

        # Try different possible cookie names (including the actual one: "cookie")
        for cookie_name in ["cookie", "session", "session_token", "auth_token", "access_token"]:
            for cookie in cookies:
                if cookie.name == cookie_name:
                    logger.info(f"Found session cookie: {cookie.name}")
                    return cookie.value

        # If no standard cookie found, return the first cookie value
        if cookies:
            first_cookie = list(cookies)[0]
            logger.info(f"Using first available cookie: {first_cookie.name}")
            return first_cookie.value

        return None
    
    async def test_user_websocket(self, user_name: str) -> bool:
        """Test WebSocket connection as authenticated user"""
        logger.info(f"\n🔗 Testing WebSocket connection as {user_name}...")
        
        # Get session cookie
        session_cookie = self.get_session_cookie()
        if not session_cookie:
            logger.error("❌ No session cookie found")
            return False
        
        # WebSocket URL for authenticated user
        ws_url = f"{self.ws_url}/api/v1/ws/chat/{self.conversation_id}"
        
        # Headers with session cookie (using correct cookie name)
        headers = {
            "Cookie": f"cookie={session_cookie}"
        }
        
        try:
            # Use additional_headers instead of extra_headers for newer websockets library
            async with websockets.connect(ws_url, additional_headers=headers) as websocket:
                logger.info(f"✅ {user_name} connected to WebSocket")
                
                # Wait for connection confirmation
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                logger.info(f"📨 {user_name} received: {response}")
                
                # Send a test message
                test_message = {
                    "type": "text",
                    "content": f"Hello from {user_name}! Testing single role system."
                }
                
                await websocket.send(json.dumps(test_message))
                logger.info(f"📤 {user_name} sent: {test_message['content']}")
                
                # Wait a bit for any responses
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    logger.info(f"📨 {user_name} received response: {response}")
                except asyncio.TimeoutError:
                    logger.info(f"⏰ {user_name} - No immediate response (normal)")
                
                return True
                
        except websockets.exceptions.ConnectionClosed as e:
            logger.error(f"❌ {user_name} WebSocket connection closed: {e}")
            return False
        except asyncio.TimeoutError:
            logger.error(f"❌ {user_name} WebSocket connection timeout")
            return False
        except Exception as e:
            logger.error(f"❌ {user_name} WebSocket error: {e}")
            return False
    
    async def test_customer_websocket(self, customer_id: str = "test-customer-123") -> bool:
        """Test WebSocket connection as customer"""
        logger.info(f"\n🔗 Testing WebSocket connection as Customer {customer_id}...")
        
        # WebSocket URL for customer
        ws_url = f"{self.ws_url}/api/v1/ws/chat/{self.conversation_id}?customer_id={customer_id}"
        
        try:
            async with websockets.connect(ws_url) as websocket:
                logger.info(f"✅ Customer {customer_id} connected to WebSocket")
                
                # Wait for connection confirmation
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                logger.info(f"📨 Customer received: {response}")
                
                # Send a test message
                test_message = {
                    "type": "text",
                    "content": f"Hello! I'm customer {customer_id}. I need help!"
                }
                
                await websocket.send(json.dumps(test_message))
                logger.info(f"📤 Customer sent: {test_message['content']}")
                
                # Wait a bit for any responses
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    logger.info(f"📨 Customer received response: {response}")
                except asyncio.TimeoutError:
                    logger.info(f"⏰ Customer - No immediate response (normal)")
                
                return True
                
        except websockets.exceptions.ConnectionClosed as e:
            logger.error(f"❌ Customer WebSocket connection closed: {e}")
            return False
        except asyncio.TimeoutError:
            logger.error(f"❌ Customer WebSocket connection timeout")
            return False
        except Exception as e:
            logger.error(f"❌ Customer WebSocket error: {e}")
            return False
    
    async def test_bidirectional_chat(self) -> bool:
        """Test bidirectional chat between user and customer"""
        logger.info(f"\n💬 Testing bidirectional chat...")
        
        # Get session cookie for user
        session_cookie = self.get_session_cookie()
        if not session_cookie:
            logger.error("❌ No session cookie found")
            return False
        
        user_ws_url = f"{self.ws_url}/api/v1/ws/chat/{self.conversation_id}"
        customer_ws_url = f"{self.ws_url}/api/v1/ws/chat/{self.conversation_id}?customer_id=test-customer-456"
        
        user_headers = {"Cookie": f"cookie={session_cookie}"}
        
        try:
            # Connect both user and customer
            async with websockets.connect(user_ws_url, additional_headers=user_headers) as user_ws, \
                       websockets.connect(customer_ws_url) as customer_ws:
                
                logger.info("✅ Both user and customer connected")
                
                # Wait for connection confirmations
                await asyncio.wait_for(user_ws.recv(), timeout=5.0)
                await asyncio.wait_for(customer_ws.recv(), timeout=5.0)
                
                # Customer sends message
                customer_message = {
                    "type": "text",
                    "content": "Hi! I need help with my order."
                }
                await customer_ws.send(json.dumps(customer_message))
                logger.info(f"📤 Customer: {customer_message['content']}")
                
                # User should receive customer's message
                try:
                    user_received = await asyncio.wait_for(user_ws.recv(), timeout=5.0)
                    logger.info(f"📨 User received: {user_received}")
                except asyncio.TimeoutError:
                    logger.warning("⚠️ User didn't receive customer message")
                
                # User responds
                user_message = {
                    "type": "text",
                    "content": "Hello! I'm here to help. What's your order number?"
                }
                await user_ws.send(json.dumps(user_message))
                logger.info(f"📤 User: {user_message['content']}")
                
                # Customer should receive user's message
                try:
                    customer_received = await asyncio.wait_for(customer_ws.recv(), timeout=5.0)
                    logger.info(f"📨 Customer received: {customer_received}")
                except asyncio.TimeoutError:
                    logger.warning("⚠️ Customer didn't receive user message")
                
                logger.info("✅ Bidirectional chat test completed")
                return True
                
        except Exception as e:
            logger.error(f"❌ Bidirectional chat test failed: {e}")
            return False

async def main():
    """Main test function"""
    logger.info("🚀 Starting WebSocket Single Role System Test")
    logger.info("=" * 60)
    
    tester = WebSocketTester()
    
    # Test 1: Login as admin
    logger.info("\n📋 Test 1: Admin User Authentication")
    if not tester.login("<EMAIL>", "adminpassword"):
        logger.error("❌ Admin login failed - stopping tests")
        return False
    
    # Get user info
    user_info = tester.get_current_user()
    if not user_info:
        logger.error("❌ Failed to get user info - stopping tests")
        return False
    
    # Test 2: Admin WebSocket connection
    logger.info("\n📋 Test 2: Admin WebSocket Connection")
    admin_ws_success = await tester.test_user_websocket("Admin")
    
    # Test 3: Customer WebSocket connection
    logger.info("\n📋 Test 3: Customer WebSocket Connection")
    customer_ws_success = await tester.test_customer_websocket()
    
    # Test 4: Bidirectional chat
    logger.info("\n📋 Test 4: Bidirectional Chat")
    chat_success = await tester.test_bidirectional_chat()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    logger.info(f"✅ Admin Login: {'PASS' if user_info else 'FAIL'}")
    logger.info(f"✅ Admin WebSocket: {'PASS' if admin_ws_success else 'FAIL'}")
    logger.info(f"✅ Customer WebSocket: {'PASS' if customer_ws_success else 'FAIL'}")
    logger.info(f"✅ Bidirectional Chat: {'PASS' if chat_success else 'FAIL'}")
    
    all_passed = all([user_info, admin_ws_success, customer_ws_success, chat_success])
    
    if all_passed:
        logger.info("\n🎉 ALL TESTS PASSED! Single role system is working correctly.")
        logger.info("✅ Any authenticated user can now chat with customers!")
    else:
        logger.error("\n❌ SOME TESTS FAILED! Check the logs above for details.")
    
    return all_passed

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
