# Testing Conversation Archiving Feature

This guide walks you through testing the conversation archiving feature using the provided test scripts.

## Prerequisites

1. Server running on port 8002
2. Python 3.8+ with required packages:
   ```bash
   pip install websockets aiohttp aioconsole asyncio
   ```

## Test Files

- **CU_TEST.py**: Customer-side WebSocket client
- **AG_TEST.py**: Agent-side WebSocket client with archiving capabilities

## Step-by-Step Testing Guide

### 1. Start the Server

```bash
cd /home/<USER>/Documents/Devlopment/yupcha-customerbot-ai
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8002
```

### 2. Run the Customer Client

Open a new terminal and run:

```bash
python CU_TEST.py
```

You should see output similar to:
```
Setting up a new customer and conversation...
✅ Setup complete. Conversation ID: 06874e73-0b6b-766d-8000-12dfb3637338

==================================================
🚀 Yupcha Interactive Customer Chat Client
👤 Customer ID: f7b03d72-7282-4080-b932-646483e88b32
💬 Conversation ID: 06874e73-0b6b-766d-8000-12dfb3637338
--------------------------------------------------
Type a message and press Enter. Type 'quit' to exit.
==================================================

>> Your message: 
```

Send a test message:
```
>> Your message: Hello, I need help with my order
```

### 3. Run the Agent Client

Open another terminal and run:

```bash
python AG_TEST.py
```

You should see:
```
Logging in <NAME_EMAIL>...
✅ Logged in successfully as Admin User (ID: 1)

==================================================
🚀 Yupcha Interactive Agent Chat Client
👤 Agent: <EMAIL>
--------------------------------------------------
Commands:
  'list' - List all conversations
  'archived' - List archived conversations
  'select' - Select a different conversation
  'archive' - Archive current conversation
  'unarchive' - Unarchive current conversation
  'quit' - Exit the client
==================================================

Command (select/list/archived/quit): 
```

### 4. Test Basic Conversation Flow

1. In the agent client, type `list` to see available conversations
2. Type `select` and choose the conversation created by the customer
3. You should now see the customer's message
4. Respond to the customer:
   ```
   >> Your reply: Hello! I'm here to help with your order. What's the issue?
   ```
5. In the customer client, you should see the agent's response
6. Continue the conversation between both clients

### 5. Test Archiving

1. In the agent client, exit the chat by typing `quit` in the message prompt
2. Type `archive` to archive the current conversation
3. Type `list` to verify the conversation is no longer in the default list
4. Type `archived` to see all conversations including archived ones
5. Verify the archived conversation appears in this list

### 6. Test Unarchiving

1. Type `unarchive` to restore the conversation
2. Type `list` to verify the conversation is back in the default list
3. Type `select` and choose the conversation again
4. Continue the conversation to verify it works after unarchiving

### 7. Test Edge Cases

1. Try archiving an already archived conversation
2. Try unarchiving a conversation that isn't archived
3. Test with multiple conversations
4. Test with different status values (new, open, closed)

## Expected Results

- **Archiving**: Conversation should disappear from default list but appear when including archived
- **Unarchiving**: Conversation should return to default list with status "closed"
- **Real-time messaging**: Messages should be delivered instantly between customer and agent
- **Status transitions**: Status should change correctly during archive/unarchive operations

## Troubleshooting

### Connection Issues

- Verify server is running on port 8002
- Check WebSocket URL in both test files
- Ensure Redis is running for WebSocket functionality

### Authentication Issues

- Verify agent credentials in AG_TEST.py
- Check session cookie handling

### Database Issues

- Verify migration was applied correctly
- Check conversation status values in database

## Advanced Testing

### Performance Testing

Test with a large number of conversations:
```bash
# Create 100 test conversations
for i in {1..100}; do
  python -c "import asyncio; from CU_TEST import setup_conversation; asyncio.run(setup_conversation())"
done

# Test listing with archived filter
python AG_TEST.py
# Use 'list' and 'archived' commands to compare performance
```

### Security Testing

- Try accessing conversations from different organizations
- Test permission boundaries
- Verify audit logging works correctly
