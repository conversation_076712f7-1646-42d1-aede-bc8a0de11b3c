#!/usr/bin/env python3
"""
Aggressive test for rate limiting - sends messages as fast as possible.
"""

import asyncio
import websockets
import json
import time

async def test_aggressive_rate_limiting():
    """Send messages as fast as possible to trigger rate limiting"""
    print("🧪 Testing Aggressive Rate Limiting...")
    
    conversation_id = 1
    customer_id = "aggressive_test_customer"
    
    uri = f"ws://localhost:8000/api/ws/chat/{conversation_id}?customer_id={customer_id}"
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"✅ Connected to: {uri}")
            
            # Send 30 messages as fast as possible
            rate_limited_count = 0
            successful_count = 0
            
            print("🚀 Sending 30 messages as fast as possible...")
            
            for i in range(30):
                message = {
                    "content": f"Aggressive test message {i+1}",
                    "message_type": "text"
                }
                
                try:
                    await websocket.send(json.dumps(message))
                    
                    # Try to receive response immediately
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=0.5)
                        response_data = json.loads(response)
                        
                        if response_data.get("type") == "error" and response_data.get("code") == "RATE_LIMIT_EXCEEDED":
                            rate_limited_count += 1
                            print(f"⚠️  Message {i+1}: RATE LIMITED")
                        else:
                            successful_count += 1
                            print(f"✅ Message {i+1}: Success")
                            
                    except asyncio.TimeoutError:
                        successful_count += 1
                        print(f"✅ Message {i+1}: Sent (no response)")
                        
                except Exception as e:
                    print(f"❌ Message {i+1}: Error - {e}")
                
                # No delay - send as fast as possible
            
            print(f"\n📊 Aggressive Test Results:")
            print(f"   • Total messages: 30")
            print(f"   • Successful: {successful_count}")
            print(f"   • Rate limited: {rate_limited_count}")
            
            if rate_limited_count > 0:
                print("✅ Rate limiting is working!")
            else:
                print("⚠️  No rate limiting detected")
                
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_aggressive_rate_limiting())
