#!/usr/bin/env python3
"""
Create Test Data Script
Populates the database with sample data for testing.
"""

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.session import AsyncSessionLocal
from app.crud import crud_user, crud_organization, crud_team, crud_customer, crud_chat
from app.schemas.user import UserCreate
from app.schemas.organization import OrganizationCreate
from app.schemas.team import TeamCreate
from app.schemas.customer import CustomerCreate
from app.schemas.chat import ConversationCreate
from app.models.user import UserRole

async def create_test_data():
    """Create comprehensive test data"""
    async with AsyncSessionLocal() as db:
        print("🚀 Creating test data...")
        
        # 1. Create Organizations
        print("📊 Creating organizations...")
        org1 = await crud_organization.create_organization(db, OrganizationCreate(
            name="Yupcha Technologies",
            description="AI-powered customer support platform",
            website="https://yupcha.com",
            contact_email="<EMAIL>",
            phone="******-0123",
            address="123 Tech Street, San Francisco, CA 94105"
        ))
        
        org2 = await crud_organization.create_organization(db, OrganizationCreate(
            name="Demo Company",
            description="Sample organization for testing",
            website="https://demo.com",
            contact_email="<EMAIL>"
        ))
        
        # 2. Create Teams
        print("👥 Creating teams...")
        team1 = await crud_team.create_team(db, TeamCreate(
            name="Customer Support",
            description="Primary customer support team",
            organization_id=org1.id
        ))
        
        team2 = await crud_team.create_team(db, TeamCreate(
            name="Technical Support",
            description="Technical assistance team",
            organization_id=org1.id
        ))
        
        team3 = await crud_team.create_team(db, TeamCreate(
            name="Sales Support",
            description="Sales and pre-sales support",
            organization_id=org2.id
        ))
        
        # 3. Create Users
        print("👤 Creating users...")
        
        # Admin user
        admin = await crud_user.create_user(db, UserCreate(
            email="<EMAIL>",
            full_name="System Administrator",
            password="adminpassword",
            role=UserRole.admin,
            organization_id=org1.id,
            is_active=True
        ))
        
        # Agent users
        agent1 = await crud_user.create_user(db, UserCreate(
            email="<EMAIL>",
            full_name="Alice Johnson",
            password="agentpassword",
            role=UserRole.agent,
            organization_id=org1.id,
            team_id=team1.id,
            is_active=True
        ))
        
        agent2 = await crud_user.create_user(db, UserCreate(
            email="<EMAIL>",
            full_name="Bob Smith",
            password="agentpassword",
            role=UserRole.agent,
            organization_id=org1.id,
            team_id=team2.id,
            is_active=True
        ))
        
        agent3 = await crud_user.create_user(db, UserCreate(
            email="<EMAIL>",
            full_name="Carol Davis",
            password="agentpassword",
            role=UserRole.agent,
            organization_id=org2.id,
            team_id=team3.id,
            is_active=True
        ))
        
        # 4. Create Customers
        print("🛒 Creating customers...")
        customer1 = await crud_customer.create_customer(db, CustomerCreate(
            customer_id="cust_001",
            name="John Doe",
            email="<EMAIL>",
            phone="******-0001",
            location="New York, NY",
            organization_id=org1.id
        ))
        
        customer2 = await crud_customer.create_customer(db, CustomerCreate(
            customer_id="cust_002",
            name="Jane Smith",
            email="<EMAIL>",
            phone="******-0002",
            location="Los Angeles, CA",
            organization_id=org1.id
        ))
        
        customer3 = await crud_customer.create_customer(db, CustomerCreate(
            customer_id="cust_003",
            name="Mike Wilson",
            email="<EMAIL>",
            phone="******-0003",
            location="Chicago, IL",
            organization_id=org2.id
        ))
        
        # 5. Create Conversations
        print("💬 Creating conversations...")
        conv1 = await crud_chat.create_conversation(db, ConversationCreate(
            customer_id=customer1.id,
            organization_id=org1.id
        ))

        conv2 = await crud_chat.create_conversation(db, ConversationCreate(
            customer_id=customer2.id,
            organization_id=org1.id
        ))

        conv3 = await crud_chat.create_conversation(db, ConversationCreate(
            customer_id=customer3.id,
            organization_id=org2.id
        ))
        
        print("✅ Test data created successfully!")
        print(f"📊 Organizations: {org1.id}, {org2.id}")
        print(f"👥 Teams: {team1.id}, {team2.id}, {team3.id}")
        print(f"👤 Users: Admin({admin.id}), Agents({agent1.id}, {agent2.id}, {agent3.id})")
        print(f"🛒 Customers: {customer1.id}, {customer2.id}, {customer3.id}")
        print(f"💬 Conversations: {conv1.id}, {conv2.id}, {conv3.id}")
        print("\n🔗 You can now test WebSocket with conversation IDs above!")

if __name__ == "__main__":
    asyncio.run(create_test_data())
