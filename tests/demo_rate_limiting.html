<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rate Limiting Demo - Yupcha Customer Bot AI</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .demo-section h3 {
            color: #34495e;
            margin-top: 0;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
        }
        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c0392b;
        }
        .btn-warning {
            background-color: #f39c12;
            color: white;
        }
        .btn-warning:hover {
            background-color: #e67e22;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.rate-limited {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-entry.success {
            color: #28a745;
        }
        .log-entry.error {
            color: #dc3545;
        }
        .log-entry.warning {
            color: #ffc107;
        }
        .log-entry.info {
            color: #17a2b8;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Rate Limiting Demo</h1>
        <p style="text-align: center; color: #7f8c8d;">
            Test the WebSocket rate limiting functionality of Yupcha Customer Bot AI
        </p>

        <div class="demo-section">
            <h3>🔧 Configuration</h3>
            <div class="controls">
                <label>
                    Conversation ID: 
                    <input type="number" id="conversationId" value="1" min="1">
                </label>
                <label>
                    Customer ID: 
                    <input type="text" id="customerId" value="rate_limit_demo_customer" placeholder="Customer ID">
                </label>
                <label>
                    Messages to Send: 
                    <input type="number" id="messageCount" value="25" min="1" max="100">
                </label>
                <label>
                    Send Interval (ms): 
                    <input type="number" id="sendInterval" value="100" min="50" max="5000">
                </label>
            </div>
        </div>

        <div class="demo-section">
            <h3>🎮 Controls</h3>
            <div class="controls">
                <button class="btn-primary" onclick="connectWebSocket()">Connect WebSocket</button>
                <button class="btn-danger" onclick="disconnectWebSocket()">Disconnect</button>
                <button class="btn-warning" onclick="sendBurstMessages()">Send Burst Messages</button>
                <button class="btn-primary" onclick="sendSlowMessages()">Send Slow Messages</button>
                <button onclick="clearLog()">Clear Log</button>
            </div>
        </div>

        <div class="demo-section">
            <h3>📊 Connection Status</h3>
            <div id="connectionStatus" class="status disconnected">
                Disconnected
            </div>
        </div>

        <div class="demo-section">
            <h3>📈 Statistics</h3>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="messagesSent">0</div>
                    <div class="stat-label">Messages Sent</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="messagesSuccess">0</div>
                    <div class="stat-label">Successful</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="messagesRateLimited">0</div>
                    <div class="stat-label">Rate Limited</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="messagesError">0</div>
                    <div class="stat-label">Errors</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📝 Activity Log</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let websocket = null;
        let stats = {
            sent: 0,
            success: 0,
            rateLimited: 0,
            error: 0
        };

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStats() {
            document.getElementById('messagesSent').textContent = stats.sent;
            document.getElementById('messagesSuccess').textContent = stats.success;
            document.getElementById('messagesRateLimited').textContent = stats.rateLimited;
            document.getElementById('messagesError').textContent = stats.error;
        }

        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
        }

        function connectWebSocket() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                log('WebSocket is already connected', 'warning');
                return;
            }

            const conversationId = document.getElementById('conversationId').value;
            const customerId = document.getElementById('customerId').value;
            
            const wsUrl = `ws://localhost:8000/api/ws/chat/${conversationId}?customer_id=${customerId}`;
            
            log(`Connecting to: ${wsUrl}`, 'info');
            
            websocket = new WebSocket(wsUrl);
            
            websocket.onopen = function(event) {
                log('✅ WebSocket connected successfully', 'success');
                updateConnectionStatus('connected', 'Connected');
            };
            
            websocket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    
                    if (data.type === 'error' && data.code === 'RATE_LIMIT_EXCEEDED') {
                        log(`⚠️ Rate limited: ${data.detail}`, 'warning');
                        stats.rateLimited++;
                        updateConnectionStatus('rate-limited', 'Rate Limited');
                    } else if (data.type === 'message') {
                        log(`📨 Message received: ${data.content}`, 'success');
                        stats.success++;
                    } else {
                        log(`📨 Received: ${JSON.stringify(data)}`, 'info');
                    }
                } catch (e) {
                    log(`📨 Received (raw): ${event.data}`, 'info');
                }
                
                updateStats();
            };
            
            websocket.onerror = function(error) {
                log(`❌ WebSocket error: ${error}`, 'error');
                stats.error++;
                updateStats();
            };
            
            websocket.onclose = function(event) {
                log(`🔌 WebSocket closed: Code ${event.code}, Reason: ${event.reason}`, 'warning');
                updateConnectionStatus('disconnected', 'Disconnected');
            };
        }

        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
                log('🔌 WebSocket disconnected', 'info');
                updateConnectionStatus('disconnected', 'Disconnected');
            }
        }

        function sendMessage(content) {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket not connected', 'error');
                return false;
            }

            const message = {
                content: content,
                message_type: 'text'
            };

            try {
                websocket.send(JSON.stringify(message));
                stats.sent++;
                updateStats();
                return true;
            } catch (error) {
                log(`❌ Failed to send message: ${error}`, 'error');
                stats.error++;
                updateStats();
                return false;
            }
        }

        async function sendBurstMessages() {
            const messageCount = parseInt(document.getElementById('messageCount').value);
            const interval = parseInt(document.getElementById('sendInterval').value);
            
            log(`🚀 Sending ${messageCount} messages with ${interval}ms interval`, 'info');
            
            for (let i = 1; i <= messageCount; i++) {
                const content = `Burst message ${i}/${messageCount} - Testing rate limiting`;
                
                if (sendMessage(content)) {
                    log(`📤 Sent message ${i}: ${content}`, 'info');
                }
                
                if (i < messageCount) {
                    await new Promise(resolve => setTimeout(resolve, interval));
                }
            }
            
            log(`✅ Finished sending ${messageCount} messages`, 'success');
        }

        async function sendSlowMessages() {
            const messageCount = 10;
            const interval = 3000; // 3 seconds
            
            log(`🐌 Sending ${messageCount} messages slowly (${interval}ms interval)`, 'info');
            
            for (let i = 1; i <= messageCount; i++) {
                const content = `Slow message ${i}/${messageCount} - Should not be rate limited`;
                
                if (sendMessage(content)) {
                    log(`📤 Sent slow message ${i}: ${content}`, 'info');
                }
                
                if (i < messageCount) {
                    await new Promise(resolve => setTimeout(resolve, interval));
                }
            }
            
            log(`✅ Finished sending ${messageCount} slow messages`, 'success');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            stats = { sent: 0, success: 0, rateLimited: 0, error: 0 };
            updateStats();
            log('📝 Log cleared', 'info');
        }

        // Initialize
        log('🎯 Rate Limiting Demo initialized', 'info');
        log('📋 Rate limit: 20 messages per 60 seconds', 'info');
        log('🔧 Click "Connect WebSocket" to start testing', 'info');
    </script>
</body>
</html>
