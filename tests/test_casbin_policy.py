import pytest
from app.core.permissions import enforcer, reload_policy
import asyncio

@pytest.mark.asyncio
async def test_admin_can_read_permissions():
    # Ensure policies are reloaded from the database before testing
    await reload_policy()

    # Assuming admin role is "Admin" and company_id is 1
    # You might need to adjust company_id based on your admin user's actual company_id
    admin_role = "Admin"
    resource_object = "company:1:permissions"
    action = "read"

    # Perform the enforcement check
    has_permission = enforcer.enforce(admin_role, resource_object, action)

    print(f"\nDEBUG: Casbin enforce check for role='{admin_role}', object='{resource_object}', action='{action}' -> {has_permission}")

    assert has_permission is True, f"Admin role should have '{action}' permission on '{resource_object}'"

@pytest.mark.asyncio
async def test_non_existent_role_cannot_read_permissions():
    await reload_policy()
    non_existent_role = "NonExistentRole"
    resource_object = "company:1:permissions"
    action = "read"

    has_permission = enforcer.enforce(non_existent_role, resource_object, action)
    assert has_permission is False, f"Non-existent role should NOT have '{action}' permission on '{resource_object}'"