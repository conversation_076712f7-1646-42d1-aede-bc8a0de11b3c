#!/usr/bin/env python3
"""
Check Database Data Script
Shows what data exists in the database.
"""

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from app.db.session import AsyncSessionLocal
from app.crud import crud_user, crud_organization, crud_team, crud_customer, crud_chat

async def check_data():
    """Check what data exists in the database"""
    async with AsyncSessionLocal() as db:
        print("🔍 Checking database data...")
        
        # Check Organizations
        orgs = await crud_organization.get_organizations(db)
        print(f"\n📊 Organizations ({len(orgs)}):")
        for org in orgs:
            print(f"  - ID: {org.id}, Name: {org.name}")
        
        # Check Teams
        teams = await crud_team.get_teams(db)
        print(f"\n👥 Teams ({len(teams)}):")
        for team in teams:
            print(f"  - ID: {team.id}, Name: {team.name}, Org: {team.organization_id}")
        
        # Check Users
        users = await crud_user.get_all_users(db)
        print(f"\n👤 Users ({len(users)}):")
        for user in users:
            print(f"  - ID: {user.id}, Name: {user.full_name}, Email: {user.email}, Role: {user.role}")
        
        # Check Customers
        customers = await crud_customer.get_customers(db)
        print(f"\n🛒 Customers ({len(customers)}):")
        for customer in customers:
            print(f"  - ID: {customer.id}, Customer ID: {customer.customer_id}, Name: {customer.name}")
        
        # Check Conversations
        conversations = await crud_chat.get_conversations(db)
        print(f"\n💬 Conversations ({len(conversations)}):")
        for conv in conversations:
            print(f"  - ID: {conv.id}, Customer: {conv.customer_id}, Org: {conv.organization_id}, Status: {conv.status}")
        
        print(f"\n✅ Database check completed!")
        
        if conversations:
            print(f"\n🔗 You can test WebSocket with conversation ID: {conversations[0].id}")
        else:
            print(f"\n⚠️ No conversations found. Create one first!")

if __name__ == "__main__":
    asyncio.run(check_data())
