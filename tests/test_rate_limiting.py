#!/usr/bin/env python3
"""
Test script for rate limiting functionality in WebSocket endpoints.
Tests both chat and notification rate limiting.
"""

import asyncio
import websockets
import json
import time
from typing import List, Dict

# Configuration
BASE_WS_URL = "ws://localhost:8000/api/ws"
CHAT_ENDPOINT = f"{BASE_WS_URL}/chat"
NOTIFICATIONS_ENDPOINT = f"{BASE_WS_URL}/agent-notifications"

# Test parameters
RATE_LIMIT = 20  # Messages per window
WINDOW_SECONDS = 60  # Rate limit window
TEST_MESSAGES = 25  # Send more than the limit

async def test_chat_rate_limiting():
    """Test rate limiting on chat WebSocket endpoint"""
    print("🧪 Testing Chat Rate Limiting...")
    
    conversation_id = 1
    customer_id = "test_customer_rate_limit"
    
    uri = f"{CHAT_ENDPOINT}/{conversation_id}?customer_id={customer_id}"
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"✅ Connected to chat WebSocket: {uri}")
            
            # Send messages rapidly to trigger rate limiting
            rate_limited_count = 0
            successful_count = 0
            
            for i in range(TEST_MESSAGES):
                message = {
                    "content": f"Test message {i+1} for rate limiting",
                    "message_type": "text"
                }
                
                try:
                    await websocket.send(json.dumps(message))
                    
                    # Try to receive response
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        response_data = json.loads(response)
                        
                        if response_data.get("type") == "error" and response_data.get("code") == "RATE_LIMIT_EXCEEDED":
                            rate_limited_count += 1
                            print(f"⚠️  Message {i+1}: Rate limited")
                        else:
                            successful_count += 1
                            print(f"✅ Message {i+1}: Sent successfully")
                            
                    except asyncio.TimeoutError:
                        successful_count += 1
                        print(f"✅ Message {i+1}: Sent (no immediate response)")
                        
                except Exception as e:
                    print(f"❌ Message {i+1}: Failed to send - {e}")
                
                # Small delay between messages
                await asyncio.sleep(0.1)
            
            print(f"\n📊 Rate Limiting Test Results:")
            print(f"   • Total messages sent: {TEST_MESSAGES}")
            print(f"   • Successful messages: {successful_count}")
            print(f"   • Rate limited messages: {rate_limited_count}")
            print(f"   • Expected rate limit: {RATE_LIMIT} messages per {WINDOW_SECONDS}s")
            
            if rate_limited_count > 0:
                print("✅ Rate limiting is working correctly!")
            else:
                print("⚠️  No rate limiting detected - check configuration")
                
    except Exception as e:
        print(f"❌ Chat rate limiting test failed: {e}")

async def test_notification_rate_limiting():
    """Test rate limiting on notification WebSocket endpoint"""
    print("\n🧪 Testing Notification Rate Limiting...")
    
    # This would require a valid session cookie
    # For now, we'll just test connection
    try:
        async with websockets.connect(NOTIFICATIONS_ENDPOINT) as websocket:
            print(f"✅ Connected to notifications WebSocket: {NOTIFICATIONS_ENDPOINT}")
            
            # This will likely fail due to authentication, but that's expected
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                print(f"📨 Received: {response}")
            except asyncio.TimeoutError:
                print("⏰ No immediate response (expected for auth failure)")
                
    except websockets.exceptions.ConnectionClosedError as e:
        if "Authentication" in str(e) or "1008" in str(e):
            print("✅ Notification endpoint properly requires authentication")
        else:
            print(f"❌ Unexpected connection error: {e}")
    except Exception as e:
        print(f"❌ Notification test failed: {e}")

async def test_rate_limit_recovery():
    """Test that rate limiting recovers after the window expires"""
    print("\n🧪 Testing Rate Limit Recovery...")
    
    conversation_id = 1
    customer_id = "test_customer_recovery"
    
    uri = f"{CHAT_ENDPOINT}/{conversation_id}?customer_id={customer_id}"
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"✅ Connected for recovery test: {uri}")
            
            # Send messages to trigger rate limiting
            print("📤 Sending messages to trigger rate limiting...")
            for i in range(RATE_LIMIT + 5):  # Exceed the limit
                message = {
                    "content": f"Recovery test message {i+1}",
                    "message_type": "text"
                }
                await websocket.send(json.dumps(message))
                await asyncio.sleep(0.05)  # Very fast sending
            
            # Check if we're rate limited
            test_message = {
                "content": "Test if rate limited",
                "message_type": "text"
            }
            
            await websocket.send(json.dumps(test_message))
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                response_data = json.loads(response)
                
                if response_data.get("code") == "RATE_LIMIT_EXCEEDED":
                    print("✅ Rate limiting is active")
                    
                    # Wait a bit and test again (in real scenario, would wait full window)
                    print("⏰ Waiting 5 seconds to test partial recovery...")
                    await asyncio.sleep(5)
                    
                    await websocket.send(json.dumps({
                        "content": "Recovery test after wait",
                        "message_type": "text"
                    }))
                    
                    try:
                        response2 = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        response2_data = json.loads(response2)
                        
                        if response2_data.get("code") == "RATE_LIMIT_EXCEEDED":
                            print("✅ Rate limiting still active (expected)")
                        else:
                            print("✅ Rate limiting recovered")
                    except asyncio.TimeoutError:
                        print("✅ Message sent successfully after wait")
                        
                else:
                    print("⚠️  No rate limiting detected")
                    
            except asyncio.TimeoutError:
                print("⚠️  No rate limiting response received")
                
    except Exception as e:
        print(f"❌ Recovery test failed: {e}")

async def main():
    """Run all rate limiting tests"""
    print("🚀 Starting Rate Limiting Tests")
    print("=" * 50)
    
    # Test chat rate limiting
    await test_chat_rate_limiting()
    
    # Test notification rate limiting
    await test_notification_rate_limiting()
    
    # Test rate limit recovery
    await test_rate_limit_recovery()
    
    print("\n" + "=" * 50)
    print("🏁 Rate Limiting Tests Complete")
    
    print(f"\n📋 Test Summary:")
    print(f"   • Rate limit: {RATE_LIMIT} messages per {WINDOW_SECONDS} seconds")
    print(f"   • Test messages sent: {TEST_MESSAGES}")
    print(f"   • WebSocket endpoints tested: 2")
    print(f"   • Recovery mechanism tested: Yes")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
