#!/usr/bin/env python3
"""
Direct test of the rate limiter functionality without WebSocket.
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core.rate_limiter import RateLimiter, check_rate_limit

async def test_rate_limiter_direct():
    """Test the rate limiter directly"""
    print("🧪 Testing Rate Limiter Directly...")
    
    identifier = "test_user_123"
    
    # Create rate limiter with low limits for testing
    limiter = RateLimiter(identifier=identifier, limit=5, window=10)
    
    print(f"📊 Testing with limit: {limiter.limit} messages per {limiter.window} seconds")
    
    # Test within limit
    print("\n🔄 Testing within rate limit...")
    for i in range(limiter.limit):
        allowed = await limiter.check()
        print(f"   Message {i+1}: {'✅ Allowed' if allowed else '❌ Blocked'}")
    
    # Test exceeding limit
    print("\n🔄 Testing exceeding rate limit...")
    for i in range(3):
        allowed = await limiter.check()
        print(f"   Extra message {i+1}: {'✅ Allowed' if allowed else '❌ Blocked'}")
    
    print("\n🔄 Testing check_rate_limit function...")
    for i in range(3):
        allowed = await check_rate_limit(f"test_user_456_{i}")
        print(f"   New user {i+1}: {'✅ Allowed' if allowed else '❌ Blocked'}")

async def test_rate_limiter_recovery():
    """Test rate limiter recovery after window"""
    print("\n🧪 Testing Rate Limiter Recovery...")
    
    identifier = "test_recovery_user"
    limiter = RateLimiter(identifier=identifier, limit=3, window=5)
    
    print(f"📊 Testing with limit: {limiter.limit} messages per {limiter.window} seconds")
    
    # Exhaust the limit
    print("\n🔄 Exhausting rate limit...")
    for i in range(limiter.limit + 2):
        allowed = await limiter.check()
        print(f"   Message {i+1}: {'✅ Allowed' if allowed else '❌ Blocked'}")
    
    # Wait for recovery
    print(f"\n⏰ Waiting {limiter.window + 1} seconds for recovery...")
    await asyncio.sleep(limiter.window + 1)
    
    # Test after recovery
    print("\n🔄 Testing after recovery...")
    for i in range(3):
        allowed = await limiter.check()
        print(f"   Post-recovery message {i+1}: {'✅ Allowed' if allowed else '❌ Blocked'}")

async def main():
    """Run all direct rate limiter tests"""
    print("🚀 Starting Direct Rate Limiter Tests")
    print("=" * 50)
    
    try:
        await test_rate_limiter_direct()
        await test_rate_limiter_recovery()
        
        print("\n" + "=" * 50)
        print("🏁 Direct Rate Limiter Tests Complete")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
